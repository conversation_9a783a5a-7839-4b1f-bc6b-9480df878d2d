FROM python:3.11-slim

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1


# Set working directory
WORKDIR /app

# Install uv
RUN pip install --upgrade pip && pip install uv

# Create a non-root user and group with UID 1000, matching Kubernetes securityContext.runAsUser
# This user will run the application process.
RUN groupadd -r appgroup && useradd -r -g appgroup -u 1000 appuser

# Copy project files
COPY pyproject.toml .
COPY . .

# Install dependencies using uv
RUN uv pip install --system --no-deps .

RUN chown -R appuser:appgroup /app

USER appuser

ENV UV_CACHE_DIR=/app/.uv_cache

# Optional: If you want to install dev dependencies
# RUN uv pip install --system --no-deps --extra dev .

# Default command (adjust to your entry point)
EXPOSE 8000
CMD ["uv", "run", "./src/networking_agent/__init__.py"]
