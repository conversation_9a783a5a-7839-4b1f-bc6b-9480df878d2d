🔹 User Input:
“None of my VMs in the network of "vnet-finance-non-prod-001/snet-dev-finance-non-prod-001" can reach the internet. Why?”

🔹 Explanation & Analysis:
source: vnet-finance-non-prod-001/snet-dev-finance-non-prod-001 - ***********/27
NSG outbound rule blocks traffic to 0.0.0.0/0.

      - name: "DefaultDenyOutbound"
        description: "Deny all outbound traffic by default"
        priority: 190
        protocol: "*"
        access: "Deny"
        direction: "Outbound"
        source: ["*"]
        source_port: ["*"]
        destination: ["*"]
        destination_port: ["*"]

🔹 Root Cause:
Outbound internet access blocked by default-deny rule.

🔹 Terraform Change Required:
Add outbound rule to allow 0.0.0.0/0.

🔹 File to Edit:
azure-connectivity-non-production/spoke/config/corporate-non-production/nsg.yaml

🔹 Updated File:
hcl
Edit
      - name: "AllowOutbound"
        description: "Allow outbound traffic by default"
        priority: 185
        protocol: "*"
        access: "Allow"
        direction: "Outbound"
        source: ["*"]
        source_port: ["*"]
        destination: ["*"]
        destination_port: ["*"]
