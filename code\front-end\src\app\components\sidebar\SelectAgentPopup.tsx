"use client";

import React, { useState } from "react";

const cx = (...classes: (string | boolean | undefined)[]) =>
  classes.filter(Boolean).join(" ");

const agents = [
  { name: "Auto Selector", code: "A", status: "active" },
  { name: "Network Agent", code: "N", status: "selected" },
  { name: "DevOps Agent", code: "D", status: "coming" },
  { name: "Cloud Agent", code: "C", status: "coming" },
  { name: "VMware Agent", code: "V", status: "coming" },
  { name: "Cost Optimization", code: "O", status: "coming" },
];

const agentGradients: Record<string, string> = {
  A: "bg-[radial-gradient(circle_at_60%_40%,#00CB5500_0%,#00CB55_100%,#D2FFD1_70%,#D2FFD100_100%)]", // Auto Selector (green)
  N: "bg-[radial-gradient(circle_at_60%_40%,#f59e42_0%,#fbbf24_100%,#fef3c7_70%,#fef3c700_100%)]", // Network Agent (orange, matches chat window)
  D: "bg-[radial-gradient(circle_at_60%_40%,#6366f1_0%,#a5b4fc_100%,#e0e7ff_70%,#e0e7ff00_100%)]", // DevOps Agent (indigo)
  C: "bg-[radial-gradient(circle_at_60%_40%,#06b6d4_0%,#67e8f9_100%,#e0f2fe_70%,#e0f2fe00_100%)]", // Cloud Agent (cyan)
  V: "bg-[radial-gradient(circle_at_60%_40%,#a21caf_0%,#e879f9_100%,#f3e8ff_70%,#f3e8ff00_100%)]", // VMware Agent (purple)
  O: "bg-[radial-gradient(circle_at_60%_40%,#16a34a_0%,#bbf7d0_100%,#f0fdf4_70%,#f0fdf400_100%)]", // Cost Optimization (green)
};

interface SelectAgentPopupProps {
  onClose: () => void;
}

const SelectAgentPopup = ({ onClose }: SelectAgentPopupProps) => {
  const [isOpen, setIsOpen] = useState(true);

  const handleClose = () => {
    setIsOpen(false);
    onClose();
  };

  return (
    <>
      {/* No trigger button here, parent controls open/close */}
      {isOpen && (
        <div
          className="fixed inset-0 bg-black/30 flex items-center justify-center z-50"
          onClick={handleClose}
        >
          <div
            className="bg-white w-full max-w-md rounded-lg shadow-lg p-6 space-y-4 relative"
            onClick={(e) => e.stopPropagation()}
          >
            {/* Close button at top right */}
            <button
              onClick={handleClose}
              className="absolute top-3 right-3 text-gray-400 hover:text-gray-700 text-2xl font-bold focus:outline-none"
              aria-label="Close"
              type="button"
            >
              &times;
            </button>
            <h2 className="text-lg font-semibold text-gray-800">
              Select Agent
            </h2>

            <ul className="space-y-2">
              {agents.map((agent, idx) => (
                <li
                  key={idx}
                  className={cx(
                    "flex items-center justify-between font-weight-700 p-3 rounded-md border",
                    agent.status === "selected" ? "bg-blue-600 text-white font-weight-600 border-gray-400" : "text-black",
                    agent.status !== "coming" && (agent.status !== "selected" ? "cursor-pointer hover:bg-blue-500" : ""),
                    agent.status === "coming" && "opacity-50 cursor-not-allowed"
                  )}
                >
                  <div className={cx("flex items-center gap-3", agent.status === "selected" ? "text-white" : "text-black")}>
                    <div
                      className={cx(
                        "w-8 h-8 rounded-full flex items-center justify-center text-white text-base font-bold shadow border border-white",
                        agentGradients[agent.code]
                      )}
                    >
                      {agent.code}
                    </div>
                    <span className={cx("text-sm", agent.status === "selected" ? "text-white" : "text-black")}>{agent.name}</span>
                  </div>
                  {agent.status === "coming" && (
                    <span className="text-xs text-gray-500 italic">
                      Coming soon
                    </span>
                  )}
                </li>
              ))}
              <div className="border border-blue-400"></div>
            </ul>
            <div className="pt-2">
              <button
                onClick={handleClose}
                className="w-full px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 font-semibold text-base shadow"
              >
                Switch Agent
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default SelectAgentPopup;
