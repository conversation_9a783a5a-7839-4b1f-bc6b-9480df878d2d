locals {
  vnet_rg    = "rg-management-network-npd-mel-01"
  vnet       = "vnet-management-network-npd-mel-01"
  pe_subnet  = "snet-mgt-npd-mel-devops-ai-01"
  pe_subnet2 = "snet-mgt-npd-mel-devops-ai-02"
  app_subnet = "snet-mgt-npd-mel-devops-ai-app-01"
  aks_snet_name = "snet-mgt-npd-mel-ai-agent-aks-01"

  common_tags = tomap({
    Department          = "Enterprise Infrastructure and Cloud"
    Platform            = "AI"
    Project             = "AI-DevOps"
    Environment         = module.naming.environment_tag_value
    terraform-workspace = terraform.workspace
  })

  # we use consolidated DNS zones
  private_dns_zones_subscription_id = "a0368199-95f7-4289-ae92-401ddc1731d3"
  private_dns_zones_resource_group  = "rg-npd-network"

  role_assignments = [
    {
      scope                = azurerm_search_service.ai_search.id
      role_definition_name = "Search Index Data Contributor"
      principal_id         = azapi_resource.AIServicesResource.identity[0].principal_id
    },
    {
      scope                = azurerm_search_service.ai_search.id
      role_definition_name = "Search Index Data Contributor"
      principal_id         = azurerm_cognitive_account.openai.identity[0].principal_id
    },
    {
      scope                = azurerm_search_service.ai_search.id
      role_definition_name = "Search Index Data Reader"
      principal_id         = azapi_resource.AIServicesResource.identity[0].principal_id
    },
    {
      scope                = azurerm_search_service.ai_search.id
      role_definition_name = "Search Index Data Reader"
      principal_id         = azurerm_cognitive_account.openai.identity[0].principal_id
    },
    {
      scope                = azurerm_search_service.ai_search.id
      role_definition_name = "Search Service Contributor"
      principal_id         = azapi_resource.AIServicesResource.identity[0].principal_id
    },
    {
      scope                = azurerm_search_service.ai_search.id
      role_definition_name = "Search Service Contributor"
      principal_id         = azurerm_cognitive_account.openai.identity[0].principal_id
    },
    {
      scope                = azapi_resource.AIServicesResource.id
      role_definition_name = "Cognitive Services Contributor"
      principal_id         = azurerm_search_service.ai_search.identity[0].principal_id
    },
    {
      scope                = azurerm_cognitive_account.openai.id
      role_definition_name = "Cognitive Services Contributor"
      principal_id         = azurerm_search_service.ai_search.identity[0].principal_id
    },
    {
      scope                = azapi_resource.AIServicesResource.id
      role_definition_name = "Cognitive Services OpenAI Contributor"
      principal_id         = azurerm_search_service.ai_search.identity[0].principal_id
    },
    {
      scope                = azurerm_cognitive_account.openai.id
      role_definition_name = "Cognitive Services OpenAI Contributor"
      principal_id         = azurerm_search_service.ai_search.identity[0].principal_id
    },
    {
      scope                = azurerm_storage_account.default.id
      role_definition_name = "Storage Blob Data Contributor"
      principal_id         = azurerm_search_service.ai_search.identity[0].principal_id
    },
    {
      scope                = azurerm_storage_account.default.id
      role_definition_name = "Storage Blob Data Contributor"
      principal_id         = azapi_resource.AIServicesResource.identity[0].principal_id
    },
    {
      scope                = azurerm_storage_account.default.id
      role_definition_name = "Storage Blob Data Contributor"
      principal_id         = azurerm_cognitive_account.openai.identity[0].principal_id
    },
    {
      scope                = module.sa_blob_private_endpoint.private_endpoint_id
      role_definition_name = "Reader"
      principal_id         = azapi_resource.project.identity[0].principal_id
    },
    {
      scope                = azurerm_storage_account.default.id
      role_definition_name = "Storage Blob Data Reader"
      principal_id         = azurerm_search_service.ai_search.identity[0].principal_id
    },
    {
      scope                = azurerm_cognitive_account.openai.id
      role_definition_name = "Cognitive Services OpenAI User"
      principal_id         = azurerm_linux_web_app.nwagt.identity[0].principal_id
    },
    {
      scope                = azurerm_cognitive_account.openai.id
      role_definition_name = "Cognitive Services OpenAI User"
      principal_id         = azurerm_linux_web_app.a2a.identity[0].principal_id
    },
    {
      scope                = azurerm_cognitive_account.openai.id
      role_definition_name = "Cognitive Services OpenAI User"
      principal_id         = azurerm_linux_web_app.mcp.identity[0].principal_id
    },
    {
      scope                = azurerm_key_vault.default.id
      role_definition_name = "Key Vault Secrets User"
      principal_id         = azurerm_linux_web_app.a2a.identity[0].principal_id
    },
    {
      scope                = azurerm_key_vault.default.id
      role_definition_name = "Key Vault Secrets User"
      principal_id         = azurerm_linux_web_app.mcp.identity[0].principal_id
    },
    {
      scope                = azurerm_key_vault.default.id
      role_definition_name = "Key Vault Secrets User"
      principal_id         = azurerm_linux_web_app.nwagt.identity[0].principal_id
    },


    #   #capg
    # {
    #   scope                = module.resource_group.id
    #   role_definition_name = "Contributor"
    #   principal_id         = data.azuread_user.cadmaxkhalilian.object_id
    # },
    #     {
    #   scope                = module.resource_group.id
    #   role_definition_name = "Contributor"
    #   principal_id         = data.azuread_user.cadmcxlee.object_id
    # },

  ]
}
