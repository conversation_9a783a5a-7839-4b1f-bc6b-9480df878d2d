"use client";

import { useState } from "react";
import Icon from "../../icons/Icon";

export type ConversationHistory = {
  id: string;
  title: string;
  lastMessage: string;
  timestamp: Date;
  messages: HistoryMessage[];
};

export type HistoryMessage = {
  id: string;
  role: "user" | "assistant";
  content: string;
  timestamp: Date;
};

interface ConversationHistorySidebarProps {
  conversations: ConversationHistory[];
  selectedConversationId: string | null;
  onSelectConversation: (conversation: ConversationHistory) => void;
  onNewConversation: () => void;
}

export default function ConversationHistorySidebar({
  conversations,
  selectedConversationId,
  onSelectConversation,
  onNewConversation,
}: ConversationHistorySidebarProps) {
  const [isCollapsed, setIsCollapsed] = useState(false);

  const formatTime = (date: Date) => {
    const now = new Date();
    const diffInHours = Math.abs(now.getTime() - date.getTime()) / (1000 * 60 * 60);
    
    if (diffInHours < 24) {
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    } else if (diffInHours < 168) { // 7 days
      return date.toLocaleDateString([], { weekday: 'short' });
    } else {
      return date.toLocaleDateString([], { month: 'short', day: 'numeric' });
    }
  };

  if (isCollapsed) {
    return (
      <div className="w-12 bg-gray-50 border-r border-gray-200 flex flex-col">
        <button
          onClick={() => setIsCollapsed(false)}
          className="p-3 hover:bg-gray-100 transition-colors"
        >
          <Icon name="chevron-right" className="w-5 h-5" />
        </button>
      </div>
    );
  }

  return (
    <div className="w-80 bg-gray-50 border-r border-gray-200 flex flex-col h-full">
      {/* Header */}
      <div className="p-4 border-b border-gray-200 bg-white">
        <div className="flex items-center justify-between mb-3">
          <h2 className="text-lg font-semibold text-gray-800">Conversations</h2>
          <button
            onClick={() => setIsCollapsed(true)}
            className="p-1 hover:bg-gray-100 rounded transition-colors"
          >
            <Icon name="chevron-left" className="w-4 h-4" />
          </button>
        </div>
        
        {/* New Conversation Button */}
        <button
          onClick={onNewConversation}
          className="w-full bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center space-x-2"
        >
          <Icon name="plus" className="w-4 h-4" />
          <span>New Chat</span>
        </button>
      </div>

      {/* Conversation List */}
      <div className="flex-1 overflow-y-auto">
        {conversations.length === 0 ? (
          <div className="p-4 text-center text-gray-500">
            <p className="text-sm">No conversations yet</p>
            <p className="text-xs mt-1">Start a new chat to begin</p>
          </div>
        ) : (
          <div className="p-2">
            {conversations.map((conversation) => (
              <button
                key={conversation.id}
                onClick={() => onSelectConversation(conversation)}
                className={`w-full text-left p-3 rounded-lg mb-2 transition-colors ${
                  selectedConversationId === conversation.id
                    ? "bg-blue-100 border border-blue-200"
                    : "bg-white hover:bg-gray-100 border border-gray-200"
                }`}
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1 min-w-0">
                    <h3 className={`font-medium text-sm truncate ${
                      selectedConversationId === conversation.id
                        ? "text-blue-800"
                        : "text-gray-800"
                    }`}>
                      {conversation.title}
                    </h3>
                    <p className="text-xs text-gray-500 mt-1 truncate">
                      {conversation.lastMessage}
                    </p>
                  </div>
                  <span className="text-xs text-gray-400 ml-2 flex-shrink-0">
                    {formatTime(conversation.timestamp)}
                  </span>
                </div>
              </button>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
