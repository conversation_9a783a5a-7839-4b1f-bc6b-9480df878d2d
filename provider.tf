terraform {
  required_version = "~> 1.9"
  required_providers {
    azurerm = {
      source  = "hashicorp/azurerm"
      version = "~> 4.0"
    }
    azapi = {
      source  = "azure/azapi"
      version = "~> 2.4"
    }
    azuread = {
      source  = "hashicorp/azuread"
      version = "~> 3.1.0"
    }
  }
  cloud {
    organization = "unisuper"
    hostname     = "app.terraform.io"

    workspaces {
      name    = "agentic-ai-pov-capgemini"
      project = "agentic-ai"
    }
  }
}
provider "azurerm" {
  features {
    resource_group {
      # allow destruction of nested resources provisioned through other means
      # prevent_deletion_if_contains_resources = false
    }
  }

  # Configure a specific Subscription ID (optional)
  subscription_id = var.subscription_id
}
provider "azurerm" {
  alias = "dns"
  # skip_provider_registration = true
  features {
    resource_group {
      # allow destruction of nested resources provisioned through other means
      # prevent_deletion_if_contains_resources = false
    }
  }
  # subscription where the private DNS zone is created
  subscription_id = var.subscription_private_dns_zone_id
}
provider "azapi" {
}

provider "kubernetes" {
  host = azurerm_kubernetes_cluster.aks.kube_config[0].host
  client_certificate = base64decode(azurerm_kubernetes_cluster.aks.kube_config[0].client_certificate)
  client_key  = base64decode(azurerm_kubernetes_cluster.aks.kube_config[0].client_key)
  cluster_ca_certificate = base64decode(azurerm_kubernetes_cluster.aks.kube_config[0].cluster_ca_certificate)
}

provider "helm" {
  kubernetes = {
    host = azurerm_kubernetes_cluster.aks.kube_config[0].host
    client_certificate = base64decode(azurerm_kubernetes_cluster.aks.kube_config[0].client_certificate)
    client_key = base64decode(azurerm_kubernetes_cluster.aks.kube_config[0].client_key)
    cluster_ca_certificate = base64decode(azurerm_kubernetes_cluster.aks.kube_config[0].cluster_ca_certificate)
    }
}
