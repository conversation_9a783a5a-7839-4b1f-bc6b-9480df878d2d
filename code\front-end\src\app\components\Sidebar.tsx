"use client";

import React, { useEffect, useState } from "react";
import { list_threads, thread_history } from "../../actions/a2a";
import Icon from "../icons/Icon";
import SidebarHeader from "./SidebarHeader";
import SidebarContent from "./SidebarContent";
import ActivityContainer from "./sidebar/ActivityContainer";
import { agentspaces } from "../data/agentspaceData";
import { UIMessage, UIConversation, SidebarProps } from "../types";
import { logger } from "../utils/logger";

export default function Sidebar({
  onTicketSelect,
  threadId,
  showCreateTicket,
  setShowCreateTicket,
  showThemePopup,
  setShowThemePopup,
  theme,
  setTheme,
}: SidebarProps) {
  const [activeTab, setActiveTab] = useState<"tickets" | "chat">("tickets");
  const [showActivity, setShowActivity] = useState(false);
  const [isSearchFocused, setIsSearchFocused] = useState(false);
  const [isAgentspaceExpanded, setIsAgentspaceExpanded] = useState(false);
  const [activeAgentspace, setActiveAgentspace] = useState(agentspaces[0]);
  const [openSections, setOpenSections] = useState<Record<string, boolean>>({
    "Awaiting Input": true,
    "In Process": false,
    "Assigned to": false,
    Archive: false,
  });
  // Conversation history state
  const [conversations, setConversations] = useState<any[]>([]);
  const [loadingConversations, setLoadingConversations] = useState(false);

  useEffect(() => {
    if (activeTab === "chat") {
      setLoadingConversations(true);
      list_threads().then((data: any) => {
        logger.log("Fetched threads:", data);
        if (data?.threads) {
          // Optionally, fetch last message for each thread
          Promise.all(
            data.threads.map(async (thread: any) => {
              const history = await thread_history(thread.thread_id);
              logger.log("history=", history);
              const lastMsg = history?.messages?.length
                ? history.messages[history.messages.length - 1]
                : null;
              let lastMsgTime = "";
              if (lastMsg) {
                // Try to find a timestamp property (timestamp or created_at)
                const timeKey = Object.keys(lastMsg).find(
                  (k) => k === "timestamp" || k === "created_at"
                );
                if (timeKey && (lastMsg as any)[timeKey]) {
                  try {
                    lastMsgTime = new Date(
                      (lastMsg as any)[timeKey]
                    ).toLocaleTimeString();
                  } catch {}
                }
              }
              return {
                id: thread.thread_id,
                name: thread.title || thread.thread_id,
                time: lastMsgTime,
                lastMessage: lastMsg ? lastMsg.content : "",
                fullHistory: history?.messages || [],
              };
            })
          ).then((convs) => {
            setConversations(convs);
            setLoadingConversations(false);
          });
        } else {
          setConversations([]);
          setLoadingConversations(false);
        }
      });
    }
  }, [activeTab]);

  const toggleActivity = () => setShowActivity(!showActivity);
  const toggleAgentspace = () => setIsAgentspaceExpanded(!isAgentspaceExpanded);

  const selectAgentspace = (agentspace: (typeof agentspaces)[0]) => {
    setActiveAgentspace(agentspace);
    setIsAgentspaceExpanded(false);
  };

  const toggleSection = (title: string) => {
    setOpenSections((prev) => ({ ...prev, [title]: !prev[title] }));
  };

  const getDisplayText = () => {
    const map: Record<any, { line1: string; line2: string }> = {
      "Network Operations": { line1: "Network", line2: "Operations" },
      "Security Center": { line1: "Security", line2: "Center" },
      Infrastructure: { line1: "Infrastructure", line2: "Management" },
      Development: { line1: "Development", line2: "Environment" },
    };
    return (
      map[activeAgentspace.name] || {
        line1: activeAgentspace.name,
        line2: "Space",
      }
    );
  };

  const displayText = getDisplayText();

  if (showThemePopup) {
    return null;
  }

  return (
    <div className="flex flex-col h-screen w-80 shadow-lg bg-white border-r border-gray-200">
      <SidebarHeader
        activeAgentspace={activeAgentspace}
        isAgentspaceExpanded={isAgentspaceExpanded}
        onToggleAgentspace={toggleAgentspace}
        onSelectAgentspace={selectAgentspace}
      />

      {/* Search */}
      {showActivity ? (
        <ActivityContainer />
      ) : (
        <>
          <div className="mx-4 mt-3 relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <Icon
                name="search"
                className={`h-4 w-4 transition-colors ${
                  isSearchFocused ? "text-blue-500" : "text-gray-400"
                }`}
              />
            </div>
            <input
              type="text"
              className="w-full pl-10 pr-3 py-2 rounded border border-gray-200 text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
              onFocus={() => setIsSearchFocused(true)}
              onBlur={() => setIsSearchFocused(false)}
            />
          </div>

          {/* Tabs */}
          <div className="flex mx-4 mt-4 border-b border-gray-200">
            {["tickets", "chat"].map((tab) => (
              <button
                key={tab}
                className={`flex-1 py-2 text-sm font-medium capitalize ${
                  activeTab === tab
                    ? "border-b-2 border-blue-500 text-blue-600"
                    : "text-gray-500 hover:text-gray-700"
                }`}
                onClick={() => {
                  setActiveTab(tab as "tickets" | "chat");
                  if (tab === "tickets") {
                    setShowCreateTicket && setShowCreateTicket(true);
                  }
                }}
              >
                {tab === "chat" ? "Agent Chat" : tab}
              </button>
            ))}
          </div>

          <SidebarContent
            activeTab={activeTab}
            openSections={openSections}
            onSectionToggle={toggleSection}
            onTicketSelect={onTicketSelect}
            conversations={conversations}
            loadingConversations={loadingConversations}
          />
        </>
      )}

      {/* Footer */}
      <div className="px-4 py-3 border-t border-gray-200 bg-white">
        <button
          className="w-full py-2 rounded-lg border border-blue-500 text-blue-600 font-medium bg-white hover:bg-blue-50 transition-all shadow-sm hover:shadow flex items-center justify-center text-center"
          style={{ justifyContent: "center", alignItems: "center" }}
          onClick={() => {
            if (activeTab === "tickets") {
              setShowCreateTicket && setShowCreateTicket(true);
            } else if (activeTab === "chat") {
              if (onTicketSelect)
                onTicketSelect({ id: "", desc: "", messages: [] });
              setShowCreateTicket && setShowCreateTicket(false);
            }
          }}
        >
          <span className="flex items-center gap-2 w-full justify-center align-middle">
            <Icon name="plus" className="h-4 w-4 relative top-[2px]" />
            <span>
              {activeTab === "tickets" ? "New Ticket" : "New Chat"}
            </span>
          </span>
        </button>
      </div>

      <div className="mt-auto border-t border-gray-200 px-4 py-3 flex items-center justify-between bg-gradient-to-r from-blue-600 to-blue-500 shadow-inner">
        <div className="flex items-center">
          <div className="w-10 h-10 rounded-full flex items-center justify-center font-bold text-white mr-3 bg-sky-500">
            U
          </div>
          <div>
            <div className="font-medium text-sm text-white font-geist-sans">
              User
            </div>
          </div>
        </div>
        <button
          className="ml-2 text-blue-200 hover:text-white transition"
          onClick={() => setShowThemePopup && setShowThemePopup(true)}
        >
          <Icon name="three-dots-vertical" className="h-5 w-5" />
        </button>
      </div>
    </div>
  );
}
