import Icon from "../../icons/Icon";
import ReasoningStep from "./ReasoningStep";
import { ReasoningDetailsProps } from "@/app/types";

export default function ReasoningDetails({ steps, onClose }: ReasoningDetailsProps) {
  // Since IntermediateStep doesn't have status, we'll consider all steps as completed
  const lastCompletedIndex = steps.length - 1;

  return (
    <div className="bg-gradient-to-r from-blue-800 to-sky-500 overflow-hidden border border-blue-700 rounded">
      <div className="flex items-center justify-between p-3 text-white">
        <span className="text-sm font-medium">Reasoning</span>
        <button
          onClick={onClose}
          className="p-1 rounded-full hover:bg-white hover:bg-opacity-20 transition-colors"
        >
          <Icon name="close" className="w-4 h-4 text-white" />
        </button>
      </div>
      <div className="px-3 pb-3">
        {steps.map((step, index) => (
          <ReasoningStep
            key={index}
            step={step}
            index={index}
            isLast={index === steps.length - 1}
            isLastCompleted={index === lastCompletedIndex}
          />
        ))}
      </div>
    </div>
  );
}
