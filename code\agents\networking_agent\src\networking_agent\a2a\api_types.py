from typing import List, Union, Optional
from pydantic import BaseModel
from langchain_core.messages import HumanMessage, AIMessage, ToolMessage
import json
import logging


class ThreadSummary(BaseModel):
	thread_id: str
	title: str

class ListThreadsResponse(BaseModel):
	threads: List[ThreadSummary]

class CustomHumanMessage(BaseModel):
	type: str = "human"
	content: str
	
class IntermediateStep(BaseModel):
	title: str
	content: str

class Reference(BaseModel):
	content: str
	name: str

class CustomAIMessage(BaseModel):
	type: str = "ai"
	content: str
	intermediate_steps: List[IntermediateStep]
	references: Optional[List[Reference]] = []

class ThreadHistoryResponse(BaseModel):
	thread_id: str
	messages: List[Union[CustomAIMessage, CustomHumanMessage]]

class QueryResponse(BaseModel):
	response: CustomAIMessage
	
class Ticket(BaseModel):
	ticket_number: str
	title: str
	raised_by: str
	open_date: str
	description: str



class ConversationTypeMapper:
	def __init__(self):
		pass
	
	
	def extract_tool_messages(self, messages: List[HumanMessage | AIMessage | ToolMessage]) -> tuple[List[ToolMessage], List[HumanMessage | AIMessage]]:
		try:
			tool_messages = [m for m in messages if isinstance(m, ToolMessage)]
			other_messages = [m for m in messages if not isinstance(m, ToolMessage)]
			return tool_messages, other_messages
		except Exception as e:
			logging.error(f"Error separating tool call results from message history: {e}")
			return [], messages
	

	def combine_tool_calls_and_messages(self, tool_messages: List[ToolMessage], other_messages: List[HumanMessage | AIMessage]):
		try:
			for tool_message in tool_messages: # Get results from tool messages and add to tool calls in AI messages
				for message in [m for m in other_messages if isinstance(m, AIMessage) and hasattr(m, 'tool_calls') and m.tool_calls]:
					for tool_call in message.tool_calls:
						if tool_call["id"] == tool_message.tool_call_id:
							tool_call["result"] = tool_message.content
		except Exception as e:
			logging.error(f"Error associating tool calls with relevant AI messages: {e}")

	
	def generate_intermediate_steps(self, tool_call):
		references = []
		intermediate_step = IntermediateStep(title=f'Calling {tool_call["name"]} with {tool_call["args"]}', content="No result")
		if "result" in tool_call.keys():
			
			if "read_file" == tool_call["name"]:
				try:
					intermediate_step_title = f'Reading file \'{tool_call["args"]["path"]}\' from the \'{tool_call["args"]["repository_name"]}\' repository.'
					intermediate_step_content = f"Successfully read the contents."
					intermediate_step = IntermediateStep(title=intermediate_step_title, content=intermediate_step_content)
					reference_name = f'{tool_call["args"]["repository_name"]}/{tool_call["args"]["path"]}'
					reference_content = tool_call["result"]
					reference = Reference(name=reference_name, content=reference_content)
					references.append(reference)
				except Exception as e:
					logging.error(f"Failed while parsing intermediate steps for read_file {e}")

			elif "search_files" == tool_call["name"]:
				try:
					filepaths = [result["filepath"] for result in json.loads(tool_call["result"])["search_results"]]
					intermediate_step_title = f'Searching for exact matches with term \'{tool_call["args"]["query"]}\''
					intermediate_step_content = f"Found {len(filepaths)} from search: \n"+", \n".join(filepaths)
					intermediate_step = IntermediateStep(title=intermediate_step_title, content=intermediate_step_content)
				except Exception as e:
					intermediate_step_title = f'There was an error searching for exact matches with term \'{tool_call["args"]["query"]}\''
					intermediate_step_content = str(e)
					intermediate_step = IntermediateStep(title=intermediate_step_title, content=intermediate_step_content)
			
			elif "semantic_search_files" == tool_call["name"]:
				try:
					filepaths = [result["filepath"] for result in json.loads(tool_call["result"])["search_results"]]
					intermediate_step_title = f'Semantically searching for matches with term \'{tool_call["args"]["query"]}\''
					intermediate_step_content = f"Found {len(filepaths)} from search: \n"+", \n".join(filepaths)
					intermediate_step = IntermediateStep(title=intermediate_step_title, content=intermediate_step_content)
				except Exception as e:
					logging.error(f"Failed while parsing intermediate steps for semantic_search_files {e}")
		
		return intermediate_step, references


	def extract_steps_and_references(self, message: AIMessage):
		intermediate_steps = []
		references = []
		if hasattr(message, "tool_calls") and message.tool_calls:
			try:
				for tool_call in message.tool_calls:
					intermediate_step, new_references = self.generate_intermediate_steps(tool_call)
					intermediate_steps.append(intermediate_step)
					references += new_references
			except Exception as e:
				logging.error(f"Error while extracting steps and references: {e}")
		return intermediate_steps, references


	def convert_history(self, messages: List[HumanMessage | AIMessage | ToolMessage]):
		
		response_messages = []
		running_ai_message = None

		tool_messages, human_ai_messages = self.extract_tool_messages(messages)
		self.combine_tool_calls_and_messages(tool_messages, human_ai_messages)

		for message in human_ai_messages: # Go through and parse the MessageHistoryResponse
			if isinstance(message, HumanMessage):
				try:
					if running_ai_message: # Add the last AI message before the human message
						response_messages.append(running_ai_message)
						running_ai_message = None
					custom_message = CustomHumanMessage(content=message.content) # Add human message to list
					response_messages.append(custom_message)
				except Exception as e:
					logging.error(f"Error adding custom messages to the list: {e}")

			if isinstance(message, AIMessage):
				try:
					content = ""
					intermediate_steps = []
					references = []
					try:
						if hasattr(message, "content"):
							content = message.content
						intermediate_steps, references = self.extract_steps_and_references(message)
					except Exception as e:
						logging.error(f"Error while reading AI message contents: {e}")

					if running_ai_message: # Add references and old AI message a intermediate step (for consecutive AI messages without a human message)
						try:
							if hasattr(running_ai_message, "content"):
								thinking_intermediate_step = IntermediateStep(title="Thinking...", content=running_ai_message.content)
								intermediate_steps = running_ai_message.intermediate_steps + [thinking_intermediate_step] + intermediate_steps
							else: # Dont add old AI message as intermediate step if the contents is empty (just calling tool)
								intermediate_steps = running_ai_message.intermediate_steps + intermediate_steps
							new_references_list = running_ai_message.references + references
							running_ai_message = CustomAIMessage(content=content, intermediate_steps=intermediate_steps, references=new_references_list)
						except Exception as e:
							logging.error(f"Error dealing with consecutive AI messages: {e}")
				
					else: # Make new AI message
						try:
							running_ai_message = CustomAIMessage(content=content,intermediate_steps=intermediate_steps, references=references)
						except Exception as e:
							logging.error(f"Failed to create a new AI message: {e}")

				except Exception as e:
					logging.error(f"Error parsing custom AI messages: {e}")

		if running_ai_message:
			response_messages.append(running_ai_message)
		return response_messages


def convert_history(messages: List[Union[HumanMessage, AIMessage, ToolMessage]]) -> List[Union[CustomAIMessage, CustomHumanMessage]]:
	try:
		converted_messages = ConversationTypeMapper().convert_history(messages)
		return converted_messages
	except Exception as e:
		return [CustomAIMessage(content=f"An unexpected error has occurred, please try again in a new conversation: {e}", intermediate_steps=[], references=[])]
	