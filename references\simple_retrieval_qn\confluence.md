1. Tell me the differences between Azure Cloud NGFW and VM series from Palo Alto. Show me how was it being deployed and used in Unisuper Azure environment.
2. What is Azure virtual WAN (vWAN) used for?
3. The ExpressRoute circuit is in used, where was it terminated to?
4. What is the use of "vnet-fwmgt-prd-mel"? 
5. What is the use of load balancer "lb-npd-mel-conn-fw-intlb" and "lb-npd-mel-conn-fw-extlb"? What are the differences between them?
6. Are there virtual routers on palo alto firewalls? What are they used for?

<!-- GCP/SDWAN related but in confluence -->
7. How many new VPCs does GCP Firewalls introduced?
8. What is the Private Service Peering for?
9. Where are all the vGWs situated?
10. Where does vGWslearn Azure vNet prefixes from?