data "azurerm_subnet" "pe_snet" {
  name                 = local.pe_subnet
  virtual_network_name = local.vnet
  resource_group_name  = local.vnet_rg
}
data "azurerm_subnet" "pe_subnet2" {
  name                 = local.pe_subnet2
  virtual_network_name = local.vnet
  resource_group_name  = local.vnet_rg
}
data "azurerm_subnet" "app_snet" {
  name                 = local.app_subnet
  virtual_network_name = local.vnet
  resource_group_name  = local.vnet_rg
}
data "azurerm_virtual_network" "app_vnet" {
  name                = local.vnet
  resource_group_name = local.vnet_rg
}

data "azurerm_client_config" "current" {}

# data "azuread_user" "cadmcxlee" {
#   user_principal_name = "<EMAIL>"
# }
# data "azuread_user" "cadmaxkhalilian" {
#   user_principal_name = "<EMAIL>"
# }
data "azurerm_subscription" "current" {}


data "azurerm_kubernetes_service_versions" "aks_current" {
    location        = var.location
    include_preview = false
}

data "azurerm_subnet" "aks-snet" {
    name                    = local.aks_snet_name
    virtual_network_name    = local.vnet
    resource_group_name     = local.vnet_rg
}

# data "azurerm_resource_group" "rg" {
#     name = local.key_vault_rg
# }

# data "azurerm_key_vault" "kv" {
#     resource_group_name = local.key_vault_rg
#     name                = local.key_vault_name
# }

data "azurerm_key_vault_key" "linux_ssh_key" {
    name         = "ssh-key-linux-profile"
    key_vault_id = azurerm_key_vault.default.id
}

# data "azurerm_container_registry" "acr" {
#     name                = "acrprdmel"
#     resource_group_name = "rg-acr-prd-mel"
#     provider            = azurerm.management-production
# }

data "azuread_service_principal" "venafi-spn" {
  display_name = "VenafiTPP-NonProd"
}
