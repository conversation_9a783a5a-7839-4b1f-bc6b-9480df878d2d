🔹 User Input:
“My Azure Load Balancer "lb-cyberark-int-npd-mel-01" backend pool is showing unhealthy. Can you help?”

🔹 Explanation & Analysis:
Azure load balancer health probe using Azure special IP which is ************* (private ip)
Health probe from ************* blocked by NSG.

🔹 Root Cause:
NSG missing allow rule for Azure health probes.

🔹 Terraform Change Required:
Add rule to allow probe IP.

🔹 File to Edit:
azure-connectivity-non-production/spoke-config/identity-non-production/nsg.yaml

🔹 Updated File:
hcl
Copy
Edit
    nsgs:
      - name: nsg-cyberark-npd-mel-01
        do_not_apply_default_rules: false
        rules:
          - name: "AllowNVAInBound"
            description: ""
            priority: 100
            protocol: "*"
            access: "Allow"
            direction: "Inbound"
            source: ["*************"] # Special private ip for health probe
            source_port: ["*"]
            destination: ["***********/26", "*************/26"]
            destination_port: ["*"]