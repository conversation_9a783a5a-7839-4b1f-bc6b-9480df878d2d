# Merlin

Merlin is an agentic framework built for UniSuper, built using LangGraph, A2A, and MCP. 

## Services
#### This repository includes three folders, each containing the microservices:
- `\agents` contains A2A servers for each agent
- `\front-end` contains the frontend Next.js server
- `\mcp` contains MCP servers for each agent

### Agents
Each agent is based on LangGraph, (although also compatible with other frameworks), and it runs an A2A server when started.

#### Networking Agent: 
Contained in `\agents\networking_agent` is the network troubleshooting agent that uses the `\mcp\networking_mcp` MCP server to run tools and fetch resources. The python version and dependencies for this service are listed in `\agents\networking_agent\.python-version` and `\agents\networking_agent\pyproject.toml` respectively.

### MCP

#### Networking MCP
Contained in `\mcp\networking_mcp` is the MCP server for the network troubleshooting agent. The python version and dependencies for this service are listed in `\mcp\networking_mcp\.python-version` and `\mcp\networking_mcp\pyproject.toml` respectively.

### Front end
Contained in `\frontend` is the frontend Next.js code which connect to the servers using the A2A protocol. The app is built on Node.js LTS (v22.16.0), and the dependencies for this app are listed in `\frontend\package.json`.

## Running Merlin

### Initial setup
#### Database initialisation script 
If you have not run the agent with the postgres setup before, you will have to run this script to set up the database. This script only has to run once to set up the database.
```bash
cd ./agents/networking_agent
uv run python ./src/networking_agent/setup.py
```

### Front end
The frontend is built on Next.js, and require Node.js to run.

For development:
```bash
cd ./front-end
npm install
npm run dev
```

For deployment, please follow https://nextjs.org/docs/app/getting-started/deploying
```bash
cd ./front-end
npm run build
npm run start
```

The host and port can be specified as environment variables `HOSTNAME` and `PORT` respectively.​

### Networking Agent
This project uses uv as the package and project manager for each agent.
```bash
cd ./agents/networking_agent
uv venv
uv sync
uv run ./src/networking_agent/__init__.py
```

The host and port can be specified using the `--host` and `--port` flags in the startup command. For example: ​
```bash
uv run ./src/networking_agent/__init__.py --host 0.0.0.0 --port 8000
```

### MCP server for Networking Agent

Before running the MCP server, you must set up git credentials, refer to the `README` in `mcp/networking_mcp` for a more detailed description of the setup.

This project uses uv as the package and project manager for each MCP server.
```bash
cd ./mcp/networking_mcp
uv venv
uv sync
uv run mcp run ./src/networking_mcp/__init__.py --transport sse
```

The host and port can be specified as environment variables `MCP_HOST` and `MCP_PORT` respectively.

## Other Scripts

### Deleting Conversation History

**WARNING: FOLLOWING THESE STEPS WILL PERMANENTLY DELETE EVERY CONVERSATION STORED IN THE DATABASE.**

The conversation history can be deleted via the reset database script.
```
cd ./agents/networking_agent
uv venv
uv sync
uv run ./src/networking_agent/reset_database.py
```
