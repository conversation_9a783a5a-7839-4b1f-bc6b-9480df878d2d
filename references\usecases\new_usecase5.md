🔹 User Input:
“We are not able to access the storage account qcwntsmxnxdm4functions for blob storage from vpn. Help me.”

🔹 Explanation & Analysis:
Storage account has to be associated with private endpoint.
1. Check if private endpoint is created - required another repo access
2. Check the network where the private endpoint is sitting in "vnet-finance-non-prod-001/snet-management-pe-npd-001"
3. Check the NSG if its allow from VPN range "**********/20"

🔹 Root Cause:
Traffic is blocked by NSG to the required private endpoint

🔹 Terraform Change Required:
Update rule to allow VPN IP ranges.

🔹 File to Edit:
azure-connectivity-non-production/spoke/config/corporate-non-production/nsg.yaml

🔹 Updated File:
hcl
Copy
Edit
      - name: "Allow_VPN"
        description: ""
        priority: 180
        protocol: "TCP"
        access: "Allow"
        direction: "Inbound"
        source: ["**********/20"]
        source_port: ["*"]
        destination: ["*************/28"]
        destination_port: ["443"]
