trigger: none
#schedules:
#  - cron: "0 0 * * *"
#    branches:
#      include: [ feature/initial ]
#    always: true

pool:
  name: 'aks-ado-agent-npd'

variables:
  storageAccount: "sadevopsainpdmel"
  containerName: "testcontainer"
  serviceConnection: "ado-management-federate-sc-management-non-production-Agentic-AI"

stages:
  - stage: SyncRepos
    jobs:
      - job: Sync_A
        steps:
          - checkout: git://Connectivity/azure-connectivity-non-production@main
            persistCredentials: true
            fetchDepth: 0
            clean: true
            displayName: 'Repo azure-connectivity-non-production'
          - template: sync-template.yml
            parameters:
              repoName: 'azure-connectivity-non-production'

      - job: Sync_B
        steps:
          - checkout: git://Connectivity/azure-next-gen-firewall-vwan@main
            persistCredentials: true
            fetchDepth: 0
            clean: true
            displayName: 'Repo azure-next-gen-firewall-vwan'
          - template: sync-template.yml
            parameters:
              repoName: 'azure-next-gen-firewall-vwan'

      - job: Sync_C
        steps:
          - checkout: git://Connectivity/azure-usm-non-production-network@main
            persistCredentials: true
            fetchDepth: 0
            clean: true
            displayName: 'Repo azure-usm-non-production-network'
          - template: sync-template.yml
            parameters:
              repoName: 'azure-usm-non-production-network'