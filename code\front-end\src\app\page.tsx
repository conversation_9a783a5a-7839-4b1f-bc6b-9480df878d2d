"use client";

import { useState } from "react";
import { query_thread, create_thread } from "../actions/a2a";
import ConversationWindow from "./components/conversation/ConversationWindow";
import AttachmentArea from "./components/conversation/AttachmentArea";
import MessageInputBox from "./components/conversation/MessageInputBox";
import Sidebar from "./components/Sidebar";
import "./markdown.css";
import Icon from "./icons/Icon";
import SelectAgentPopup from "./components/sidebar/SelectAgentPopup";
import ThemePopup from "./components/ThemePopup";
import { logger } from "./utils/logger";
import { UIMessage, IntermediateStep } from "./types";

export default function Home() {
  const [query, setQuery] = useState<string>("");
  const [messages, setMessages] = useState<UIMessage[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isAttachmentAreaOpen, setIsAttachmentAreaOpen] =
    useState<boolean>(false);
  const [threadId, setThreadId] = useState<string | null>(null);
  const [conversationTitle, setConversationTitle] = useState<string>(
    "Network Troubleshooting Workplace"
  );
  const [currentReasoning, setCurrentReasoning] = useState<{
    steps: IntermediateStep[];
    isProcessing?: boolean;
  }>({ steps: [] });
  const [showCreateTicket, setShowCreateTicket] = useState(false);
  const [showAgentPopup, setShowAgentPopup] = useState(false);
  const [showThemePopup, setShowThemePopup] = useState(false);
  const [theme, setTheme] = useState("system");

  function toggleAttachmentArea() {
    setIsAttachmentAreaOpen(!isAttachmentAreaOpen);
  }

  function handleTicketSelect(ticket: any) {
    // Prevent double set if already selected
    if (threadId === ticket.id) {
      return;
    }
    setShowCreateTicket(false); // Hide form when selecting a conversation
    setThreadId(ticket.id);

    let cleanDesc = ticket.desc;
    let isAgentChat = false;
    try {
      const parsed = JSON.parse(ticket.desc);
      if (parsed && typeof parsed === "object" && parsed.query) {
        cleanDesc = parsed.query;
        isAgentChat = true;
      }
    } catch (e) {}
    if (isAgentChat) {
      setConversationTitle(cleanDesc);
    } else {
      setConversationTitle(`${ticket.id} - ${cleanDesc}`);
    }

    // Convert ticket messages if they exist
    if (ticket.messages) {
      let convertedMessages: UIMessage[] = [];
      if (isAgentChat) {
        // Agent chat: parse and convert messages from backend format
        logger.log("Inside Selected ticket:", ticket.messages);
        convertedMessages = ticket.messages.map((msg: any, idx: number) => {
          logger.log("Inside map Selected ticket:", msg.intermediate_steps);
          let content = msg.content;
          let type: "human" | "ai" = "ai";
          
          // Try to parse user message content if it's JSON
          if (msg.content && typeof msg.content === "string") {
            try {
              const parsed = JSON.parse(msg.content);
              if (parsed && typeof parsed === "object" && parsed.query) {
                content = parsed.query;
                type = "human";
              }
            } catch {
              // Not JSON, treat as AI reply
              type = "ai";
            }
          }
          
          const baseMessage = {
            id: msg.id || `${ticket.id}-${idx}`,
            content,
            timestamp: msg.timestamp ? new Date(msg.timestamp) : new Date(),
          };

          if (type === "ai") {
            return {
              ...baseMessage,
              type: "ai" as const,
              intermediate_steps: Array.isArray(msg.intermediate_steps) 
                ? msg.intermediate_steps 
                : [],
              references: msg.references || [],
            } as UIMessage;
          } else {
            return {
              ...baseMessage,
              type: "human" as const,
            } as UIMessage;
          }
        });
      } else {
        // Ticket: convert old format to new format
        convertedMessages = ticket.messages.map((msg: any, idx: number) => {
          const baseMessage = {
            id: msg.id || `${ticket.id}-${idx}`,
            content: msg.content,
            timestamp: msg.timestamp ? new Date(msg.timestamp) : new Date(),
          };

          // Convert old role system to type system
          if (msg.role === "assistant" || msg.role === "ai") {
            return {
              ...baseMessage,
              type: "ai" as const,
              intermediate_steps: msg.intermediate_steps || [],
              references: msg.references || [],
            } as UIMessage;
          } else {
            return {
              ...baseMessage,
              type: "human" as const,
            } as UIMessage;
          }
        });
      }
      setMessages(convertedMessages);
    } else {
      setMessages([]);
    }
    setQuery("");
  }

  async function handleNewConversation() {
    setShowCreateTicket(false); // Hide form when starting a new conversation
    setThreadId(null);
    setConversationTitle("Network Troubleshooting Workplace");
    setMessages([]);
    setQuery("");
    
    try {
      const newThread = await create_thread();
      if(!newThread){
        throw("Unkown error creating thread");
      }
      setThreadId(newThread.thread_id);
      setConversationTitle("New Conversation");
    } catch (error) {
      logger.error("Failed to create new thread:", error);
      // Fallback to UUID if thread creation fails
      const fallbackId = crypto.randomUUID();
      setThreadId(fallbackId);
      setConversationTitle(`New Conversation (${fallbackId})`);
    }
  }

  async function handleSubmit(query: string) {
    if (!query.trim()) return;
    
    let currentThreadId = threadId;
    
    // Create new thread if none exists
    if (!currentThreadId) {
      try {
        const newThread = await create_thread();
        if(!newThread){
          throw("Unknown error creating thread");
        }
        currentThreadId = newThread?.thread_id;
        setThreadId(currentThreadId);
        setConversationTitle(query.trim());
      } catch (error) {
        logger.error("Failed to create new thread:", error);
        currentThreadId = crypto.randomUUID();
        setThreadId(currentThreadId);
        setConversationTitle(`New Conversation (${currentThreadId})`);
      }
    }
  
    const userMessage: UIMessage = {
      id: Date.now().toString(),
      type: "human",
      content: query,
      timestamp: new Date(),
    };
  
    // Only add the user message - no temporary AI message
    setMessages((prev: UIMessage[]) => [...prev, userMessage]);
    
    // Set loading state to true - this will trigger the thinking message in ConversationWindow
    setIsLoading(true);
  
    try {
      const answer = await query_thread(query, currentThreadId);
      logger.log("Answer received:", answer);
  
      if (answer?.response?.content) {
        // Add the AI response as a new message
        const assistantMessage: UIMessage = {
          id: Date.now().toString(),
          type: "ai",
          content: answer.response.content,
          intermediate_steps: answer.response.intermediate_steps || [],
          references: answer.response.references || [],
          timestamp: new Date(),
        };
  
        setMessages((prev) => [...prev, assistantMessage]);
      } else {
        logger.error("No response content received");
        // You could add an error message here if needed
      }
    } catch (error) {
      logger.error("Error in handleSubmit:", error);
      // You could add an error message here if needed
    } finally {
      setIsLoading(false);
      setCurrentReasoning({ steps: [], isProcessing: false });
    }
  }

  logger.log("Messages page:", messages);

  return (
    <div className="flex h-screen bg-gray-100 relative">
      {showThemePopup ? (
        <>
          {/* Sidebar as a vertical strip with back icon and name, avatar in footer */}
          <div className="flex flex-col items-center w-20 bg-gradient-to-b from-blue-900 to-blue-500 border-r border-gray-200 py-6 justify-between h-screen">
            {/* Back icon at the top */}
            <button
              className="mt-2 mb-4 p-2 rounded-full hover:bg-blue-700 transition-colors"
              onClick={() => setShowThemePopup(false)}
              title="Back"
            >
              <Icon name="chevron-left" className="w-6 h-6 text-white" />
            </button>
            {/* Spacer to push avatar to bottom */}
            <div className="flex-1" />
            {/* Avatar and name in footer */}
            <div className="flex flex-col items-center mb-2">
              <div className="w-12 h-12 rounded-full flex items-center justify-center font-bold text-white bg-sky-500 mb-1">
                M
              </div>
            </div>
          </div>
          {/* Main Setting Window */}
          <div className="flex-1 flex flex-col">
            {/* Header with 'Setting' */}
            <div className="p-4 bg-white shadow-sm flex items-center">
              <h2 className="text-lg font-semibold text-gray-800">Setting</h2>
            </div>
            {/* Theme window */}
            <ThemePopup
              onClose={() => setShowThemePopup(false)}
              theme={theme}
              setTheme={setTheme}
            />
          </div>
        </>
      ) : (
        <>
          {/* Left Sidebar */}
          <Sidebar
            onTicketSelect={handleTicketSelect}
            threadId={threadId}
            showCreateTicket={showCreateTicket}
            setShowCreateTicket={setShowCreateTicket}
            showThemePopup={showThemePopup}
            setShowThemePopup={setShowThemePopup}
            theme={theme}
            setTheme={setTheme}
          />
          {/* Main Chat Area */}

          <div className="border-l-8 border-r-8 border-b-8 flex flex-col flex-1 bg-gray-200 border-gray-200">
            {/* Chat header */}
            <div className="border-b border-gray-300 p-4 bg-gray-200 shadow-sm flex items-center justify-between">
              <div className="flex items-center space-x-3 -ml-4">
                {threadId && (
                  <button
                    onClick={handleNewConversation}
                    className="p-1 rounded-full hover:bg-gray-300 transition-colors"
                    title="Back to main conversation"
                  >
                    {/* <Icon name="arrow-left" className="w-5 h-5 text-gray-600" /> */}
                  </button>
                )}
                <h2 className="text-lg font-semibold text-gray-800">
                  {showThemePopup ? "Setting" : conversationTitle}
                </h2>
              </div>

              {/* Network Agent */}
              <div className="flex items-center">
                {/* Settings Icon - Left and outside of Network Agent block */}
                <div
                  className="flex items-center px-4 mr-2"
                  onClick={() => setShowAgentPopup(true)}
                >
                  <Icon
                    name="settings"
                    className="w-6 h-6 text-black cursor-pointer"
                  />
                </div>

                {/* Network Agent Block */}
                <div className="flex items-center space-x-2 bg-white px-4 py-2 rounded-[4px]">
                  <div className="w-6 h-6 rounded-full bg-gradient-to-b from-orange-500 to-orange-100 flex items-center justify-center">
                    <img
                      src="/n.png"
                      alt="N"
                      width="12"
                      height="12"
                      className="object-contain"
                    />
                  </div>
                  <span className="text-base font-medium text-gray-700">
                    Network Agent
                  </span>
                </div>
              </div>
            </div>

            {/* Conversation Window */}
            <ConversationWindow
              messages={messages}
              isLoading={isLoading}
              currentReasoning={currentReasoning}
              showCreateTicket={showCreateTicket}
            />

            {/* Attachment Area */}
            <AttachmentArea
              isOpen={isAttachmentAreaOpen}
              onToggle={toggleAttachmentArea}
            />

            {/* Message Input Box */}
            <MessageInputBox
              query={query}
              setQuery={setQuery}
              isLoading={isLoading}
              isAttachmentAreaOpen={isAttachmentAreaOpen}
              onToggleAttachment={toggleAttachmentArea}
              onSubmit={handleSubmit}
            />

            {/* Agent Popup */}
            {showAgentPopup && (
              <SelectAgentPopup onClose={() => setShowAgentPopup(false)} />
            )}
          </div>
        </>
      )}

      {/* Animation styles */}
      <style jsx>{`
        @keyframes slideUp {
          from {
            transform: translateY(20px);
            opacity: 0;
          }
          to {
            transform: translateY(0);
            opacity: 1;
          }
        }
        .animate-slideUp {
          animation: slideUp 0.2s ease-out forwards;
        }
      `}</style>
    </div>
  );
}
