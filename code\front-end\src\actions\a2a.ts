"use server";

import { A2AClient } from "../a2a/src/client/client";
import { Task, TaskQueryParams, TaskSendParams, TextPart } from "../a2a/src/schema";
import { v4 as uuidv4 } from "uuid";
import { ListThreadsResponse, QueryResponse, ThreadHistoryResponse, ThreadSummary, NewThreadIDResponse } from "./types";
import { NextRequest, NextResponse } from "next/server";
import { config } from "../app/utils/config";
import { logger } from "../app/utils/logger";

const client = new A2AClient(config.a2aUrl);

export async function list_threads(): Promise<ListThreadsResponse|undefined> {
  try {
    const response = await fetch(`${config.a2aUrl}/threads/list/chat`)
    const data = await response.json()
    return data as ListThreadsResponse
  }
  catch (error) {
    logger.error("Error fetching list of threads:", error)
  }
}

export async function list_ticket_threads(): Promise<ListThreadsResponse|undefined> {
  try {
    const response = await fetch(`${config.a2aUrl}/threads/list/ticket`)
    const data = await response.json()
    return data as ListThreadsResponse
  }
  catch (error) {
    logger.error("Error fetching list of ticket threads:", error)
  }
}

export async function thread_history(threadID: string): Promise<ThreadHistoryResponse|undefined> {
  if (!threadID) {
    logger.warn("thread_history called with blank threadID");
    return undefined;
  }
  try {
    const postData = {thread_id: threadID}
    const response = await fetch(`${config.a2aUrl}/threads/history`, {
      method: 'POST',
      headers: {
          'Content-Type': 'application/json',
      },
      body: JSON.stringify(postData)
    })
    const data = await response.json()
    return data as ThreadHistoryResponse
  }
  catch (error) {
    logger.error("Error fetching thread history:", error)
  }
}

export async function query_thread(query: string, threadID: string): Promise<QueryResponse|undefined> {
  try {
    const taskId = uuidv4();
    const sendData = {
      type: "text", 
      text: JSON.stringify({
        query: query, 
        thread_id: threadID
      })
    } as TextPart
    
    const sendParams: TaskSendParams = {
      id: taskId,
      message: { role: "user", parts: [sendData] },
      thread_id: threadID,
      customParams: { thread_id: threadID },
    };
    await client.sendTask(sendParams);

    const getParams: TaskQueryParams = { id: taskId };
    const getTaskResult: Task | null = await client.getTask(getParams);
    const textPart = getTaskResult?.status.message?.parts[0] as TextPart
    const data = JSON.parse(textPart.text)
    return data as QueryResponse
  } 
  catch (error) {
    logger.error("Error querying thread:", error)
  }
}


/*export async function POST(req: NextRequest) {
  try {
    const { query, threadID } = await req.json();
    const result = await query_thread(query, threadID);
    return NextResponse.json(result);
  } catch (error) {
    logger.error("API route error:", error);
    return NextResponse.json({ error: "Failed to process query" }, { status: 500 });
  }
}*/

export async function create_thread(): Promise<NewThreadIDResponse|undefined> {
  try {
    const response = await fetch(`${process.env.A2A_URL}/threads/new`, {
      method: 'POST',
      headers: {
          'Content-Type': 'application/json',
      }
    })
    const data = await response.json()
    return data as NewThreadIDResponse
  }
  catch {
    console.error("Error fetching a new thread_id")
  }
}

export interface Ticket {
  ticket_number: string;
  title: string;
  raised_by: string;
  open_date: string;
  description: string;
}

export async function create_ticket_thread(ticket: Ticket, threadID: string): Promise<QueryResponse|undefined> {
  try {
    const taskId = uuidv4();
    const query = ticket.description
    const sendData = {
      type: "text", 
      text: JSON.stringify({
        query: query, 
        ticket: ticket,
        thread_id: threadID
      })
    } as TextPart
    
    const sendParams: TaskSendParams = {
      id: taskId,
      message: { role: "user", parts: [sendData] },
      thread_id: threadID,
      customParams: { thread_id: threadID },
    };
    await client.sendTask(sendParams);

    const getParams: TaskQueryParams = { id: taskId };
    const getTaskResult: Task | null = await client.getTask(getParams);
    const textPart = getTaskResult?.status.message?.parts[0] as TextPart
    const data = JSON.parse(textPart.text)
    return data as QueryResponse
  } 
  catch (error) {
    logger.error("Error querying thread:", error)
  }
}