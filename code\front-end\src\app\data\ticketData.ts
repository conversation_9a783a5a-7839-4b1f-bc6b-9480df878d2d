import { TicketData, Message } from "../types";

export const ticketSections = [
  {
    title: "Awaiting Input",
    count: 2,
    tickets: [
      { 
        id: "TX123456", 
        desc: "Server unreachable",
        messages: [
          {
            id: "tx123456-1",
            type: "human" as const,
            content: "TX123456 Server unreachable - The server at 192.168.1.100 is not responding to ping requests. Can you help diagnose the issue?",
            timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),
          },
          {
            id: "tx123456-2",
            type: "ai" as const,
            content: `I'll help you diagnose the server connectivity issue. Let me check a few things:\n\n1. **Network Connectivity**: First, let's verify if this is a network routing issue\n2. **Server Status**: Check if the server is powered on and running\n3. **Firewall Rules**: Verify firewall configurations\n\nCan you try running a traceroute to 192.168.1.100 and share the results?
            Here's a sample API response format:

\`\`\`json
{
    "status": "success",
    "data": {
        "user_id": 12345,
        "username": "john_doe",
        "email": "<EMAIL>"
    },
    "timestamp": "2024-01-15T10:30:00Z"
}
\`\`\`

This is a typical REST API response structure.`,
            timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000 + 30000),
            intermediate_steps: [],
            references: []
          }
        ]
      },
      { 
        id: "TX789012", 
        desc: "Network latency issues",
        messages: [
          {
            id: "tx789012-1",
            type: "human" as const,
            content: "TX789012 Network latency issues - We're experiencing network latency issues across our office network. Users are reporting slow response times when accessing internal resources.",
            timestamp: new Date(Date.now() - 24 * 60 * 60 * 1000),
          },
          {
            id: "tx789012-2",
            type: "ai" as const,
            content: `Here's a simple Python function to check network connectivity:

\`\`\`python
import requests

def check_connectivity(url):
    try:
        response = requests.get(url, timeout=5)
        return response.status_code == 200
    except:
        return False

print(check_connectivity("https://google.com"))
\`\`\`

This function returns \`True\` if the connection is successful.`,
            timestamp: new Date(Date.now() - 24 * 60 * 60 * 1000 + 45000),
            intermediate_steps: [],
            references: []
          }
        ]
      },
    ],
  },
  {
    title: "In Process",
    count: 3,
    tickets: [
      { 
        id: "TX345678", 
        desc: "DNS configuration",
        messages: [
          {
            id: "tx345678-1",
            type: "human" as const,
            content: "TX345678 DNS configuration - Our DNS server is not resolving external domains properly. Internal resolution works fine.",
            timestamp: new Date(Date.now() - 3 * 60 * 60 * 1000),
          },
          {
            id: "tx345678-2",
            type: "ai" as const,
            content: "DNS resolution issues for external domains typically involve:\n\n**Check these areas:**\n1. Forwarder configuration\n2. Root hints\n3. Firewall rules for port 53\n4. ISP DNS servers\n\nCan you run `nslookup google.com` and share the output?",
            timestamp: new Date(Date.now() - 3 * 60 * 60 * 1000 + 15000),
            intermediate_steps: [],
            references: []
          }
        ]
      },
      { 
        id: "TX901234", 
        desc: "Firewall settings",
        messages: [
          {
            id: "tx901234-1",
            type: "human" as const,
            content: "TX901234 Firewall settings - Need to configure firewall rules for new application deployment on port 8080.",
            timestamp: new Date(Date.now() - 5 * 60 * 60 * 1000),
          }
        ]
      },
      { 
        id: "TX567890", 
        desc: "VPN connection failure",
        messages: [
          {
            id: "tx567890-1",
            type: "human" as const,
            content: "TX567890 VPN connection failure - Remote users cannot connect to VPN. Getting authentication errors.",
            timestamp: new Date(Date.now() - 6 * 60 * 60 * 1000),
          }
        ]
      },
    ],
  },
  {
    title: "Assigned to",
    count: 1,
    tickets: [
      { 
        id: "TX234567", 
        desc: "Router configuration",
        messages: [
          {
            id: "tx234567-1",
            type: "human" as const,
            content: "TX234567 Router configuration - Need to update routing table for new subnet 10.0.5.0/24.",
            timestamp: new Date(Date.now() - 8 * 60 * 60 * 1000),
          }
        ]
      }
    ],
  },
  {
    title: "Archive",
    count: 0,
    tickets: [],
  },
];
