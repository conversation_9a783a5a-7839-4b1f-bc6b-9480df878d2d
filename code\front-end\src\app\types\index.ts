// Extended UI types built on canonical API types
import { 
  Message, 
  CustomAIMessage, 
  CustomHumanMessage,
  ThreadSummary,
  IntermediateStep,
  Reference 
} from '@/actions/types';

// Sidebar specific types
export type SectionKey = "Awaiting Input" | "In Process" | "Assigned to" | "Archive";

export interface TicketData {
  id: string;
  desc: string;
  messages: Message[];
}

// Minimal UI extensions to base types
export interface UICustomAIMessage extends CustomAIMessage {
  id: string;
  timestamp: Date;
}

export interface UICustomHumanMessage extends CustomHumanMessage {
  id: string;
  timestamp: Date;
}

export type UIMessage = UICustomAIMessage | UICustomHumanMessage;

// Extended thread info for UI
export interface UIThreadSummary extends ThreadSummary {
  lastActivity?: Date;
  messageCount?: number;
}

// Conversation state for UI components
export interface UIConversation {
  id: string;
  title: string;
  messages: UIMessage[];
  lastActivity: Date;
}

// Chat component types
export interface ChatMessageProps {
  id: string;
  type: "human" | "ai";
  content: string;
  timestamp: Date;
  intermediate_steps?: IntermediateStep[];
  references?: Reference[];
  onCloseReasoning?: () => void;
  showAttachments?: boolean;
  isLoading?: boolean;
}

export interface ReasoningDetailsProps {
  steps: IntermediateStep[];
  onClose: () => void;
}

// Component-specific props
export interface ConversationWindowProps {
  messages: UIMessage[];
  isLoading: boolean;
  currentReasoning?: {
    steps: IntermediateStep[];
    isProcessing?: boolean;
  };
  showCreateTicket?: boolean;
}

export interface AttachmentAreaProps {
  isOpen: boolean;
  onToggle: () => void;
}

export interface SidebarProps {
  onTicketSelect: (ticket: TicketData) => void;
  threadId: string | null;
  showCreateTicket: boolean;
  setShowCreateTicket: (show: boolean) => void;
  showThemePopup: boolean;
  setShowThemePopup: (show: boolean) => void;
  theme: string;
  setTheme: (theme: string) => void;
}

export interface SectionProps {
  title: string;
  count: number;
  tickets: TicketData[];
  isOpen: boolean;
  onToggle: (title: string) => void;
  onTicketSelect?: (ticket: TicketData) => void;
}

export interface SidebarContentProps {
  activeTab: "tickets" | "chat";
  openSections: Record<string, boolean>;
  onSectionToggle: (title: string) => void;
  onTicketSelect: (ticket: TicketData) => void;
  conversations: any[];
  loadingConversations: boolean;
  selectedThreadId: string | null;
}

// Re-export API types for convenience
export type { 
  Message, 
  CustomAIMessage, 
  CustomHumanMessage,
  ThreadSummary,
  IntermediateStep,
  Reference,
  ListThreadsResponse,
  ThreadHistoryResponse,
  QueryResponse
} from '@/actions/types';

