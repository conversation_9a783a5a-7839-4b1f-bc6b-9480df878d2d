apiVersion: apps/v1
kind: Deployment
metadata:
  name: frontend-app
  namespace: networking
  labels:
    app: frontend-app
    version: v1
spec:
  replicas: 1
  selector:
    matchLabels:
      app: frontend-app
  template:
    metadata:
      labels:
        app: frontend-app
        version: v1
    spec:
      containers:
      - name: frontend-app
        image: acrsbxl5binteg.azurecr.io/agentic-ai/frontend:v1
        ports:
        - containerPort: 3000
          name: http-web
        envFrom:
        - configMapRef:
            name: networking-agent-config
        - secretRef:
            name: networking-agent-secret
        resources:
          requests:
            memory: "128Mi"
            cpu: "100m"
          limits:
            memory: "512Mi"
            cpu: "300m"
        # Liveness and Readiness Probes (Optional but Recommended for Production)
        # If the app has health endpoints in your frontend app,
        # you can uncomment and configure these.
        # livenessProbe:
        #   httpGet:
        #     path: /api/health # Adjust to your actual health endpoint
        #     port: 3000
        #   initialDelaySeconds: 30
        #   periodSeconds: 10
        #   timeoutSeconds: 5
        #   failureThreshold: 3
        # readinessProbe:
        #   httpGet:
        #     path: /api/health # Adjust to your actual health endpoint
        #     port: 3000
        #   initialDelaySeconds: 5
        #   periodSeconds: 5
        #   timeoutSeconds: 3
        #   failureThreshold: 3
        securityContext:
          allowPrivilegeEscalation: false
          runAsNonRoot: true
          runAsUser: 1000 
          capabilities:
            drop:
            - ALL
      imagePullSecrets:
      - name: acr-secret 
      restartPolicy: Always