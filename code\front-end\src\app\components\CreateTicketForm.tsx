"use client";

import React, { useState } from 'react';
import { logger } from "../utils/logger";
import { create_thread, create_ticket_thread, Ticket } from '@/actions/a2a';

const CreateTicketForm = () => {
  const [formData, setFormData] = useState({
    ticketNumber: '',
    ticketTitle: '',
    raisedBy: '',
    openDate: '',
    description: '',
  });

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    logger.log('Form submitted:', formData);

    const newThread = await create_thread();
    if(!newThread){
      throw("Unkown error creating thread");
    }
    
    const ticket: Ticket = {
      ticket_number: formData.ticketNumber,
      title: formData.ticketTitle,
      raised_by: formData.raisedBy,
      open_date: formData.openDate,
      description: formData.description,
    }

    await create_ticket_thread(ticket, newThread.thread_id)
    logger.log('Created new ticket');
  };

  return (
    <form onSubmit={handleSubmit} className="w-full bg-white p-10">
      {/* Two-column grid for first four fields, label left, input right */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* Ticket Number */}
        <div className="flex items-center gap-2">
          <label className="w-32 text-left text-sm font-medium text-gray-700 mb-0">Ticket Number</label>
          <input
            type="text"
            name="ticketNumber"
            value={formData.ticketNumber}
            onChange={handleChange}
            className="flex-1 border border-gray-300 rounded-md p-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>
        {/* Ticket Title */}
        <div className="flex items-center ml-18">
          <label className="w-28 text-left text-sm font-medium text-gray-700 mb-0">Ticket Title</label>
          <input
            type="text"
            name="ticketTitle"
            value={formData.ticketTitle}
            onChange={handleChange}
            className="flex-1 border border-gray-300 rounded-md p-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>
        {/* Ticket Raised By */}
        <div className="flex items-center gap-2">
          <label className="w-32 text-left text-sm font-medium text-gray-700 mb-0">Ticket Raised By</label>
          <input
            type="text"
            name="raisedBy"
            value={formData.raisedBy}
            onChange={handleChange}
            className="flex-1 border border-gray-300 rounded-md p-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>
        {/* Open Date */}
        <div className="flex items-center ml-18">
          <label className="w-28 text-left text-sm font-medium text-gray-700 mb-0">Open Date</label>
          <input
            type="text"
            name="openDate"
            value={formData.openDate}
            onChange={handleChange}
            className="flex-1 border border-gray-300 rounded-md p-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>
      </div>

      {/* Ticket Description (label on top) */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2 mt-4">Ticket Description</label>
        <textarea
          name="description"
          rows={4}
          value={formData.description}
          onChange={handleChange}
          className="w-full border border-gray-300 rounded-md p-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
        />
      </div>

      {/* Upload area (label on top) */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mt-4 mb-2">Attachments or screenshots</label>
        <div className="border-2 border-dashed border-blue-300 rounded-md p-6 text-center bg-gray-200">
          <p className="text-sm text-gray-900 mb-2">Drag and drop files here</p>
            <p className="text-xs text-gray-900 mb-4">or</p>
          <button
            type="button"
            className="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded hover:bg-blue-700"
          >
            Browse file
          </button>
        </div>
      </div>

      <div className="text-center">
        <button
          type="submit"
          className="px-6 py-2 mt-4 bg-white text-blue-700 border border-blue-700 rounded-md hover:bg-blue-50 transition-colors font-medium text-sm shadow-sm"
        >
          Assign to Network Troubleshooting Agent
        </button>
      </div>
    </form>
  );
};

export default CreateTicketForm;
