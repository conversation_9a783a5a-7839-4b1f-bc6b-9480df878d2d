import os
from azure.core.credentials import AzureKeyCredential
from azure.search.documents import SearchClient
from azure.search.documents.models import VectorizedQuery
from dotenv import load_dotenv
from openai import AzureOpenAI

load_dotenv()

AZURE_SEARCH_SERVICE_ENDPOINT = os.environ["AZURE_SEARCH_SERVICE_ENDPOINT"]
AZURE_SEARCH_INDEX_NAME = os.environ["AZURE_SEARCH_INDEX_NAME"]
AZURE_SEARCH_API_KEY = os.environ["AZURE_SEARCH_API_KEY"]
AZURE_OPENAI_ENDPOINT = os.environ["AZURE_OPENAI_ENDPOINT"]
AZURE_OPENAI_KEY = os.getenv("AZURE_OPENAI_KEY")
AZURE_OPENAI_EMBEDDING_DEPLOYMENT = os.getenv("AZURE_OPENAI_EMBEDDING_DEPLOYMENT")
EMBEDDING_MODEL_NAME = os.getenv("EMBEDDING_MODEL_NAME")
AZURE_OPENAI_API_VERSION = os.getenv("AZURE_OPENAI_API_VERSION")

def create_search_client():
    return SearchClient(AZURE_SEARCH_SERVICE_ENDPOINT, AZURE_SEARCH_INDEX_NAME, AzureKeyCredential(AZURE_SEARCH_API_KEY))

def search_text(search_text: str):
    search_client = create_search_client()
    results = search_client.search(query_type='full', search_text=search_text, search_mode="all", include_total_count=True)
    return {"search_results": [{"filepath": result["filepath"], "search_score": result["@search.score"]} for result in results]}

def search_semantic(search_text, k=7):
    embedding_client = AzureOpenAI(
        azure_deployment=AZURE_OPENAI_EMBEDDING_DEPLOYMENT,
        api_version=AZURE_OPENAI_API_VERSION,
        azure_endpoint=AZURE_OPENAI_ENDPOINT,
        api_key=AZURE_OPENAI_KEY,
        timeout=60,
        max_retries=2,
    )
    embedding = embedding_client.embeddings.create(input=search_text, model=EMBEDDING_MODEL_NAME).data[0].embedding
    search_client = create_search_client()
    vector_query = VectorizedQuery(vector=embedding, k_nearest_neighbors=k, fields="text_vector")
    results = search_client.search(  
        search_text=None,  
        vector_queries= [vector_query],
        select=["title", "chunk", "repoName", "filepath"],
        semantic_max_wait_in_milliseconds=30000,
    )
    return {"search_results": [{"filepath": result["filepath"], "search_score": result["@search.score"]} for result in results]}

if __name__ == "__main__":
    search_client = create_search_client()
    search_query = input("Please enter your search query")
    results = search_client.search(query_type='full', search_text=search_query, search_mode="all", include_total_count=True)
    for result in results:
        print(result["repoName"])