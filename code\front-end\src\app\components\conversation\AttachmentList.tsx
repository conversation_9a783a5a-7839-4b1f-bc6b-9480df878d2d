import { useState } from 'react';
import AttachmentItem from './AttachmentItem';
import Icon from '@/app/icons/Icon';
import { Reference } from '@/actions/types';

const AttachmentList = ({ references }: { references: Reference[] }) => {
  const [showAll, setShowAll] = useState(false);

  const hasMore = references.length > 4;

  const toggleShowAll = () => {
    setShowAll(!showAll);
  };

  if (!showAll) {
    // Show first 4 cards in a single row of 5, and the Show more button as the 5th grid item at the end of the row
    return (
      <div className="mt-4 w-full px-12">
        {references.length > 0 && (
          <span className='text-gray-800 mb-6 block'>Source:</span>
        )}
        <div className="grid grid-cols-3 gap-4 w-full">
          {references.slice(0, 4).map((reference, index) => (
            <div key={index} className="min-w-[160px]">
              <AttachmentItem {...reference} />
            </div>
          ))}
          {/* Show More Button as a grid item at the end of the row */}
          {hasMore && (
            <div style={{ width: "65px", minWidth: "78" }}>
              <button
                className="flex flex-col items-center justify-center bg-gray-200 rounded-lg p-2 transition-colors cursor-pointer w-full h-full min-w-[48px]"
                onClick={toggleShowAll}
              >
                <Icon name="chevron-right" className="w-5 h-5 text-gray-600" />
                <span className="text-xs text-gray-600 font-medium text-center">
                  Show more
                </span>
              </button>
            </div>
          )}
        </div>
      </div>
    );
  }

  // When expanded: show all cards in rows of 4, with Show less button as the next grid item after the last card (in the same grid)
  if (showAll) {
    const items = references.map((item, idx) => (
      <div key={idx} className="min-w-[140px]">
        <AttachmentItem name={item.name} content={item.content} />
      </div>
    ));
    // Add the Show less button as the next grid item
    items.push(
      <div key="show-less" style={{ width: "65px", minWidth: "78" }}>
        <button
          className="flex flex-col items-center justify-center bg-gray-200 rounded-lg p-2 transition-colors cursor-pointer w-full h-full min-w-[48px]"
          onClick={toggleShowAll}
        >
          <Icon name="chevron-left" className="w-5 h-5 text-gray-600" />
          <span className="text-xs text-gray-600 font-medium text-center">
            Show less
          </span>
        </button>
      </div>
    );
    // Render in rows of 5
    const rows = [];
    for (let i = 0; i < items.length; i += 5) {
      rows.push(
        <div
          key={i}
          className="grid grid-cols-5 gap-4 w-full mb-3 items-stretch"
        >
          {items.slice(i, i + 5)}
        </div>
      );
    }
    return <div className="mt-4 w-full px-12">{rows}</div>;
  }

  return null;
};

export default AttachmentList;
