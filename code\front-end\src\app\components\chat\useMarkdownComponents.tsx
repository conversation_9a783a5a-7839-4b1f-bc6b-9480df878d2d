import { useMemo } from "react";

export function useMarkdownComponents() {
  return useMemo(() => ({
    span: ({ children }: any) => <span className="mb-2 last:mb-0">{children}</span>,
    strong: ({ children }: any) => (
      <strong className="font-semibold text-gray-900">{children}</strong>
    ),
    h1: ({ children }: any) => (
      <h1 className="text-lg font-bold mb-2 text-gray-900">{children}</h1>
    ),
    h2: ({ children }: any) => (
      <h2 className="text-base font-bold mb-2 text-gray-900">{children}</h2>
    ),
    h3: ({ children }: any) => (
      <h3 className="text-sm font-bold mb-1 text-gray-900">{children}</h3>
    ),
    ul: ({ children }: any) => (
      <ul className="list-disc list-inside mb-2 space-y-1">{children}</ul>
    ),
    ol: ({ children }: any) => (
      <ol className="list-decimal list-inside mb-2 space-y-1">{children}</ol>
    ),
    li: ({ children }: any) => <li className="text-sm">{children}</li>,
    code: ({ children }: any) => (
      <code className="bg-gray-100 px-1 py-0.5 rounded text-xs font-mono">
        {children}
      </code>
    ),
  }), []);
}
