import React from "react";

interface ThemePopupProps {
  onClose: () => void;
  theme: string;
  setTheme: (t: string) => void;
}

const ThemePopup: React.FC<ThemePopupProps> = ({ onClose, theme, setTheme }) => {
  return (
    <div className="flex flex-col h-screen w-full bg-gray-200 border-l-8 border-r-8 border-b-8 border-white justify-center items-center animate-slideUp">
      <div className="w-full max-w-md mx-auto flex flex-col items-center p-10">
        <h2 className="text-2xl font-bold mb-8 text-gray-800">Theme</h2>
        <div className="flex flex-col gap-6 items-center w-full mb-10">
          <label className="flex items-center gap-3 text-lg">
            <input type="checkbox" checked={theme === 'system'} onChange={() => setTheme('system')} />
            <span>Auto/System</span>
          </label>
          <label className="flex items-center gap-3 text-lg">
            <input type="checkbox" checked={theme === 'dark'} onChange={() => setTheme('dark')} />
            <span>Dark mode</span>
          </label>
          <label className="flex items-center gap-3 text-lg">
            <input type="checkbox" checked={theme === 'light'} onChange={() => setTheme('light')} />
            <span>Light mode</span>
          </label>
        </div>
        <button
          className="w-60 py-3 rounded bg-blue-600 text-white font-semibold text-lg hover:bg-blue-700 transition"
          onClick={onClose}
        >
          Save
        </button>
      </div>
    </div>
  );
};

export default ThemePopup;
