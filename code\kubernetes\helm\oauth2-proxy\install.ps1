# oauth2-proxy/install.ps1
param(
    [string]$Namespace = "networking",
    [string]$ReleaseName = "oauth2-proxy",
    [string]$ValuesFile = "values.yaml"
)

# Set error handling
$ErrorActionPreference = "Stop"

Write-Host "Installing/Upgrading $ReleaseName in namespace $Namespace..." -ForegroundColor Green

try {
    # Check if oauth2-proxy Helm repository is already added
    Write-Host "Checking if oauth2-proxy Helm repository is configured..." -ForegroundColor Yellow
    $repoList = helm repo list --output json 2>$null | ConvertFrom-Json
    $oauth2ProxyRepo = $repoList | Where-Object { $_.name -eq "oauth2-proxy" }
    
    if (-not $oauth2ProxyRepo) {
        Write-Host "oauth2-proxy repository not found. Adding it..." -ForegroundColor Yellow
        helm repo add oauth2-proxy https://oauth2-proxy.github.io/manifests
        
        if ($LASTEXITCODE -ne 0) {
            throw "Failed to add oauth2-proxy Helm repository."
        }
        
        Write-Host "oauth2-proxy repository added successfully." -ForegroundColor Green
    } else {
        Write-Host "oauth2-proxy repository already exists." -ForegroundColor Green
    }
    
    # Update Helm repositories to ensure we have the latest charts
    Write-Host "Updating Helm repositories..." -ForegroundColor Yellow
    helm repo update
    
    if ($LASTEXITCODE -ne 0) {
        throw "Failed to update Helm repositories."
    }

    # Create the namespace if it doesn't already exist.
    # This command is idempotent and safe to run multiple times.
    Write-Host "Creating namespace '$Namespace' if it doesn't exist..." -ForegroundColor Yellow
    kubectl create namespace $Namespace --dry-run=client -o yaml | kubectl apply -f -
    
    # Check the exit code of the last command to ensure namespace creation was successful.
    if ($LASTEXITCODE -ne 0) {
        throw "Failed to create namespace '$Namespace'."
    }

    # Install or upgrade the Helm release for oauth2-proxy.
    # Since we've already added the repository, we can reference it directly.
    # The --wait flag waits for the release to be in a ready state.
    Write-Host "Installing/Upgrading Helm release '$ReleaseName'..." -ForegroundColor Yellow
    helm upgrade --install $ReleaseName `
        oauth2-proxy/oauth2-proxy `
        --namespace $Namespace `
        --values $ValuesFile `
        --wait `
        --timeout 10m
    
    # Check the exit code of the Helm command.
    if ($LASTEXITCODE -ne 0) {
        throw "Helm installation/upgrade for '$ReleaseName' failed."
    }

    Write-Host "Successfully installed/upgraded $ReleaseName!" -ForegroundColor Green
    
    # Display the status of the release for verification.
    Write-Host "Release status for '$ReleaseName':" -ForegroundColor Cyan
    helm status $ReleaseName -n $Namespace
}
catch {
    # Catch any errors that occurred during the try block.
    Write-Error "An error occurred during the installation process: $_"
    # Exit with a non-zero status code to indicate failure, useful for CI/CD pipelines.
    exit 1
}