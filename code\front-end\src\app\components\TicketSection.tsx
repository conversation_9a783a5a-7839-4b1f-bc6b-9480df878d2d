"use client";

import { TicketData, SectionProps } from "../types";

export default function TicketSection({
  title,
  count,
  tickets,
  isOpen,
  onToggle,
  onTicketSelect,
}: SectionProps) {
  const handleTicketClick = (ticket: TicketData) => {
    if (onTicketSelect) {
      onTicketSelect(ticket);
    }
  };

  return (
    <div className="mb-2">
      {/* Section Header */}
      <button
        className="w-full flex items-center justify-between p-2 text-left hover:bg-gray-50 rounded transition-colors"
        onClick={() => onToggle(title)}
      >
        <div className="flex items-center space-x-2">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className={`h-3 w-3 text-gray-400 transition-transform ${
              isOpen ? "rotate-90" : ""
            }`}
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M9 5l7 7-7 7"
            />
          </svg>
          <span className="text-xs font-medium text-gray-700">{title}</span>
          <div className="w-4 h-4 bg-gray-300 rounded-full flex items-center justify-center">
            <span className="text-xs text-white font-medium">{count}</span>
          </div>
        </div>
      </button>

      {/* Section Content */}
      {isOpen && (
        <div className="ml-4 mt-1 space-y-1">
          {tickets.map((ticket) => (
            <div
              key={ticket.id}
              className="p-2 rounded cursor-pointer hover:bg-gray-50 transition-colors"
              onClick={() => handleTicketClick(ticket)}
            >
              <div className="flex justify-between items-start mb-1">
                <span className="text-xs font-medium text-blue-600"></span>
              </div>
              <p className="text-xs text-gray-800 leading-tight">
                {ticket.id}-{ticket.desc}
              </p>
            </div>
          ))}

          {tickets.length === 0 && (
            <div className="p-2 text-center">
              <p className="text-xs text-gray-400">No tickets</p>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
