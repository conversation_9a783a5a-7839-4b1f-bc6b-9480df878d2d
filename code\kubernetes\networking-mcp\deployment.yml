apiVersion: apps/v1
kind: Deployment
metadata:
  name: networking-agent
  namespace: networking
  labels:
    app: networking-agent
    version: v1
spec:
  replicas: 1
  selector:
    matchLabels:
      app: networking-agent
  template:
    metadata:
      labels:
        app: networking-agent
        version: v1
    spec:
      containers:
      - name: networking-agent
        image: acrsbxl5binteg.azurecr.io/agentic-ai/networking-agent:test-latest-v4
        ports:
        - containerPort: 8000
          name: http
        # env:
        # - name: PYTHONDONTWRITEBYTECODE
        #   value: "1"
        # - name: PYTHONUNBUFFERED
        #   value: "1"
        # - name: TRANSPORT_TYPE
        #   value: "sse"
        envFrom:
        - configMapRef:
            name: networking-agent-config
        - secretRef:
            name: networking-agent-secret
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        # livenessProbe:
        #   httpGet:
        #     path: /health
        #     port: 8000
        #   initialDelaySeconds: 30
        #   periodSeconds: 10
        #   timeoutSeconds: 5
        #   failureThreshold: 3
        # readinessProbe:
        #   httpGet:
        #     path: /ready
        #     port: 8000
        #   initialDelaySeconds: 5
        #   periodSeconds: 5
        #   timeoutSeconds: 3
        #   failureThreshold: 3
        securityContext:
          allowPrivilegeEscalation: false
          runAsNonRoot: true
          runAsUser: 1000
          capabilities:
            drop:
            - ALL
      imagePullSecrets:
      - name: acr-secret
      restartPolicy: Always