"use client";

import Icon from "../icons/Icon";
import { agentspaces } from "../data/agentspaceData";

interface SidebarHeaderProps {
  activeAgentspace: typeof agentspaces[0];
  isAgentspaceExpanded: boolean;
  onToggleAgentspace: () => void;
  onSelectAgentspace: (agentspace: typeof agentspaces[0]) => void;
}

export default function SidebarHeader({
  activeAgentspace,
  isAgentspaceExpanded,
  onToggleAgentspace,
  onSelectAgentspace,
}: SidebarHeaderProps) {
  return (
    <div className="flex flex-col w-full bg-gradient-to-b from-blue-900 to-blue-500 p-4 relative">
      {/* Overlay when agentspace dropdown is expanded */}
      {isAgentspaceExpanded && (
        <div onClick={onToggleAgentspace} className="fixed inset-0 z-40 bg-black/30" style={{ pointerEvents: 'auto' }} />
        <div onClick={onToggleAgentspace} className="fixed inset-0 z-40 bg-black/30" style={{ pointerEvents: 'auto' }} />
      )}
      {/* Logo Section */}
      <div className="flex flex-col items-center justify-center mb-4 mr-8">
        <div className="flex items-center justify-center">
          <img 
            src="/merlin_dog.png" 
            alt="Merlin Logo" 
            width="50" 
            height="50" 
            className="object-contain"
          />
          <span className="text-white font-bold text-2xl tracking-wider select-none font-geist-sans">
            MERLIN
            MERLIN
          </span>
        </div>
      </div>

      {/* Agentspace section */}
      <div className="relative z-50">
        <div 
          className="flex items-center p-2 bg-white cursor-pointer rounded-[4px]"
          className="flex items-center p-2 bg-white cursor-pointer rounded-[4px]"
          onClick={onToggleAgentspace}
        >
          <div className="flex items-center justify-center w-8 h-8 rounded-full bg-blue-500">
            <span className="text-white text-sm">
              <img
                src="/dashboard_icon.svg"
                alt="dashboard icon Logo"
                width="70"
                height="45"
                className="object-contain"
              />
            </span>
          </div>
          <div className="ml-3 flex-1">
            <div className="font-medium text-sm text-blue-800 font-geist-sans">
              Agentspace
            </div>
            <div className="text-xs text-gray-700">{activeAgentspace.name}</div>
          </div>
          <button className="ml-2 text-gray-600 hover:text-gray-800">
            <Icon 
              name="chevron-down" 
              className="w-4 h-4"
              isExpanded={isAgentspaceExpanded}
            />
          </button>
        </div>
        
        {/* Expanded Agentspace options */}
        {isAgentspaceExpanded && (
          <div className="absolute top-full left-0 right-0 mt-1 bg-white rounded-lg shadow-lg border border-gray-200 z-50 overflow-hidden">
            {agentspaces.map((space) => (
              <div 
                key={space.id}
                className={`flex items-center p-2.5 cursor-pointer hover:bg-gray-50 ${
                  space.id === activeAgentspace.id ? 'bg-blue-50' : ''
                }`}
                onClick={() => onSelectAgentspace(space)}
              >
                <div className="flex items-center justify-center w-8 h-8 rounded-full bg-blue-500">
                  <span className="text-white text-sm">{space.icon}</span>
                </div>
                <div className="ml-3">
                  <div className="font-medium text-sm text-gray-800 font-geist-sans">
                    {space.name}
                  </div>
                </div>
                {space.id === activeAgentspace.id && (
                  <div className="ml-auto">
                    <Icon name="check" className="w-4 h-4 text-blue-500" />
                  </div>
                )}
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
