# Network Architecture Reference

## Table of Contents
* [1. Environment - Network and CIDR]
* [2. IaC - Repositories]

## 1. Environment
### 1.1 Environment Segmentation

| Domain        | DomainName   | Existence        | ChangeControl |
|---------------|--------------|------------------|---------------|
| Production    | unisuper.lan | Azure, GCP, GCVE | Terraform     |
| NonProduction | devops.lan   | Azure, GCP, GCVE | Terraform     |

> **IMPORTANT:**  In Azure, production and non production are parked under unisuper.lan domain instead.

## 1.2 IP Address Allocation

### 1.2.1 Azure

| Environment   | SubscriptionName                          | VNetName                                  | CIDR                           | DNSServers                                 | Remark                         |
|---------------|-------------------------------------------|-------------------------------------------|--------------------------------|--------------------------------------------|--------------------------------|
| Production    | USM Production                            | vnet-cvbackup-prd-syd-01                  | ***********/26                 | *********, *********                       | only for backup                |
|               |                                           | vnet-inv-syd-01                           | *********/23                   | *********, *********, *********, ********* | legacy                         |
|               |                                           | vnet-inv-syd-01                           | *********/23                   | *********, *********, *********, ********* | legacy                         |
|               |                                           | vnet-prd-mel-01                           | 10.24.0.0/15, 192.168.100.0/22 | *********, *********, *********, 10.3.36.3 | legacy                         |
|               |                                           | vnet-prd-syd-01                           | 10.26.0.0/15, 192.168.108.0/22 | *********, *********, *********, 10.3.36.3 | legacy                         |
|               |                                           | vnet-shd-mel-01                           | 10.1.0.0/16, 192.168.104.0/22  | *********, *********, *********, 10.3.36.3 | legacy                         |
|               |                                           | vnet-shd-syd-01                           | 10.2.0.0/16, 192.168.112.0/22  | *********, *********                       | legacy                         |
| Production    | Connectivity - Production                 | vnet-connectivity-prd-mel-001             | 10.48.0.0/21                   | *********, *********, *********, 10.3.36.3 | Platform - CAF landing zone    |
|               |                                           | vnet-connectivity-prd-syd-001             | 10.56.0.0/21                   | *********, *********, *********, 10.3.36.3 | Platform - CAF landing zone    |
|               |                                           | vnet-fwmgt-prd-mel                        | 10.48.12.0/24                  | *********, *********, *********, 10.3.36.3 | Platform - CAF landing zone    |
|               |                                           | vnet-inc-network-prd-mel-01               | 10.48.14.0/23                  | *********, *********, *********, 10.3.36.3 | Platform - CAF landing zone    |
|               |                                           | vnet-sdwanedge-prd-mel                    | 10.48.10.0/23                  | Azure DNS                                  | Platform - CAF landing zone    |
|               |                                           | vnet-sdwanedge-prd-syd                    | 10.56.10.0/23                  | Azure DNS                                  | Platform - CAF landing zone    |
| Production    | Identity - Production                     | vnet-identity-prd-mel-01                  | 10.48.16.0/22                  | *********, *********, *********, 10.3.36.3 | Platform - CAF landing zone    |
|               |                                           | vnet-identity-prd-syd-01                  | 10.56.16.0/22                  | *********, *********, *********, 10.3.36.3 | Platform - CAF landing zone    |
| Production    | Management - Production                   | vnet-management-network-prd-mel-01        | 10.48.32.0/21                  | *********, *********, *********, 10.3.36.3 | Platform - CAF landing zone    |
|               |                                           | vnet-management-network-prd-syd-01        | 10.56.32.0/21                  | *********, *********                       | Platform - CAF landing zone    |
| Production    | Core Administration - Production          | vnet-rpa-prd-syd-01                       | 10.56.96.0/22                  | *********, *********, *********, 10.3.36.3 | Application - CAF landing zone |
| Production    | Corporate - Production                    | vnet-abacus-prd-001                       | 10.48.128.0/21                 | *********, *********, *********, 10.3.36.3 | Application - CAF landing zone |
| Production    | Customer Communications - Production      | NIL                                       | NIL                            | NIL                                        | Application - CAF landing zone |
| Production    | Data Management - Production              | vnet-dmgt-prd-mel-01                      | 10.48.88.0/24                  | *********, *********, *********, 10.3.36.3 | Application - CAF landing zone |
|               |                                           | vnet-erms-prd-mel-01                      | 10.48.89.0/27                  | *********, *********, *********, 10.3.36.3 | Application - CAF landing zone |
|               |                                           | vnet-erms-prd-syd-01                      | 10.56.73.0/27                  | *********, *********, *********, 10.3.36.3 | Application - CAF landing zone |
|               |                                           | vnet-mdp-ppd-mel-01                       | 10.48.240.0/23                 | *********, *********, *********, 10.3.36.3 | Application - CAF landing zone |
|               |                                           | vnet-mdp-ppd-syd-01                       | 10.56.240.0/23                 | *********, *********, *********, 10.3.36.3 | Application - CAF landing zone |
|               |                                           | vnet-mdp-prd-mel-01                       | 10.48.80.0/21                  | *********, *********, *********, 10.3.36.3 | Application - CAF landing zone |
|               |                                           | vnet-mdp-prd-syd-01                       | 10.56.80.0/21                  | *********, *********, *********, 10.3.36.3 | Application - CAF landing zone |
| Production    | Digital - Production                      | vnet-digital-prd-mel-01                   | 10.48.144.0/21                 | *********, *********, *********, 10.3.36.3 | Application - CAF landing zone |
|               |                                           | vnet-digital-prd-syd-01                   | 10.56.144.0/21                 | *********, *********, *********, 10.3.36.3 | Application - CAF landing zone |
| Production    | Employer - Production                     | NIL                                       | NIL                            | NIL                                        | Application - CAF landing zone |
| Production    | Integration - Production                  | NIL                                       | NIL                            | NIL                                        | Application - CAF landing zone |
| Production    | Investments - Production                  | vnet-inv-prd-syd-01                       | 10.56.208.0/23                 | *********, *********, *********, 10.3.36.3 | Application - CAF landing zone |
| Production    | ACS Data Archive - Production             | vnet-acs-data-archive-prod-mel-01         | 10.48.64.0/23                  | *********, *********                       | Application - CAF landing zone |
| Production    | ACS Integration - Production              | vnet-mergers-and-acquisitions-prod-mel-01 | 10.48.48.0/23                  | *********, *********                       | Application - CAF landing zone |
| Production    | Messaging - Production                    | NIL                                       | NIL                            | NIL                                        | Application - CAF landing zone |
| NonProduction | USM Non-Production                        | vnet-npd-mel-01                           | 172.24.0.0/15                  | 172.24.16.15, 10.7.116.1, 10.9.116.1       | legacy                         |
| NonProduction | Connectivity - Non-Production             | vnet-connectivity-non-prod-001            | 172.28.0.0/21                  | 172.24.16.15, 10.7.116.1, 10.9.116.1       | Platform - CAF landing zone    |
|               |                                           | vnet-sdwanedge-npd-mel                    | 172.28.10.0/23                 | Azure DNS                                  | Platform - CAF landing zone    |
| NonProduction | Identity - Non-Production                 | vnet-identity-npd-mel-01                  | 172.28.16.0/22                 | 172.24.16.15, 10.7.116.1, 10.9.116.1       | Platform - CAF landing zone    |
| NonProduction | Management - Non-Production               | vnet-management-network-npd-mel-01        | 172.28.32.0/21                 | 172.24.16.15, 10.7.116.1, 10.9.116.1       | Platform - CAF landing zone    |
| NonProduction | Core Administration - Non-Production      | vnet-rpa-npd-mel-01                       | 172.28.96.0/22                 | 172.24.16.15, 10.7.116.1, 10.9.116.1       | Application - CAF landing zone |
| NonProduction | Corporate - Non-Production                | vnet-finance-non-prod-001                 | 172.28.64.0/23                 | 172.24.16.15, 10.7.116.1, 10.9.116.1       | Application - CAF landing zone |
| NonProduction | Customer Communications - Non-Production  | NIL                                       | NIL                            | NIL                                        | Application - CAF landing zone |
| NonProduction | Data Management - Non-Production          | vnet-dmgt-npd-mel-01                      | 172.28.192.0/24                | 172.24.16.15, 10.7.116.1, 10.9.116.1       | Application - CAF landing zone |
|               |                                           | vnet-erms-npd-mel-01                      | 172.28.193.0/27                | 172.24.16.15, 10.7.116.1, 10.9.116.1       | Application - CAF landing zone |
|               |                                           | vnet-mdp-npd-mel-01                       | 172.28.80.0/21, 172.28.88.0/21 | 172.24.16.15, 10.7.116.1, 10.9.116.1       | Application - CAF landing zone |
| NonProduction | Digital - Non-Production                  | vnet-digital-npd-mel-01                   | 172.28.144.0/21                | 172.24.16.15, 10.7.116.1, 10.9.116.1       | Application - CAF landing zone |
| NonProduction | Employer - Non-Production                 | NIL                                       | NIL                            | NIL                                        | Application - CAF landing zone |
| NonProduction | Integration - Non-Production              | NIL                                       | NIL                            | NIL                                        | Application - CAF landing zone |
| NonProduction | Investments - Non-Production              | vnet-inv-npd-mel-01                       | 172.28.208.0/23                | 172.24.16.15, 10.7.116.1, 10.9.116.1       | Application - CAF landing zone |
| NonProduction | Mergers and Acquisitions - Non-Production | NIL                                       | NIL                            | NIL                                        | Application - CAF landing zone |
| NonProduction | Messaging - Non-Production                | NIL                                       | NIL                            | NIL                                        | Application - CAF landing zone |

### 1.2.2 GCP

| Environment   | Project                      | VPCName                    | Subnet                    | CIDR                           | Remark     |
|---------------|------------------------------|----------------------------|---------------------------|--------------------------------|------------|
| Production    | gcp-usm-prod-infra-filestore | NIL                        | NIL                       | NIL                            |            |
| Production    | gcp-usm-prod-infra-gcve      | NIL                        | NIL                       | NIL                            |            |
| Production    | gcp-usm-prod-mon-gcve        | gcp-to-splunk              | gcp-to-splunk-subnet      | 10.130.0.0/24                  |            |
| Production    | gcp-usm-prod-mgmt-bkp        | NIL                        | NIL                       | NIL                            |            |
| Production    | gcp-usm-prod-net-vpc         | prod-net-0                 | prod-backup-a-as1         | 10.64.27.32/27                 | Shared VPC |
|               |                              |                            | prod-landing-a-as1        | 10.64.24.0/24                  | Shared VPC |
|               |                              |                            | prod-security-a-as1       | 10.64.26.0/27                  | Shared VPC |
|               |                              | vpc-fw-mgt-prd-syd         | sn-fw-mgt-prd-syd         | 10.64.29.0/24                  | Shared VPC |
|               |                              | vpc-fw-private-prd-syd     | sn-fw-private-prd-syd     | 10.64.32.0/24                  | Shared VPC |
|               |                              | vpc-fw-public-prd-syd      | sn-fw-public-prd-syd      | 10.64.48.0/24, 10.64.52.0/22   | Shared VPC |
|               |                              | vpc-sdwanedge-inet-prd-syd | sn-sdwanedge-inet-prd-syd | 10.64.28.32/27                 | Shared VPC |
|               |                              | vpc-sdwanedge-lan-prd-syd  | sn-sdwanedge-lan-prd-syd  | 10.64.28.96/27                 | Shared VPC |
|               |                              | vpc-sdwanedge-mgt-prd-syd  | sn-sdwanedge-mgt-prd-syd  | 10.64.28.0/27                  | Shared VPC |
|               |                              | vpc-sdwanedge-vpn-prd-syd  | sn-sdwanedge-vpn-prd-syd  | 10.64.28.64/27                 | Shared VPC |
| Production    | gcp-usm-prod-sec-core        | NIL                        | NIL                       | NIL                            |            |
| Production    | gcp-usm-prod-sec-ctm         | NIL                        | NIL                       | NIL                            |            |
| Production    | gcp-usm-sec-orca-scan        | orca-vpc                   | orca-australia-southeast1 | 10.8.0.0/20                    |            |
|               |                              |                            | orca-australia-southeast2 | 10.27.0.0/20                   |            |
|               |                              |                            | orca-us-east1             | 10.4.0.0/20                    |            |
| Production    | prod-audit-logs              | gcp-to-splunk              | gcp-to-splunk-subnet      | 10.128.0.0/20                  |            |
| Production    | prod-usm-billing             | NIL                        | NIL                       | NIL                            |            |
| Production    | prod-usm-cicd                | NIL                        | NIL                       | NIL                            |            |
| NonProduction | gcp-usm-nonprod-infra-gcve   | NIL                        | NIL                       | NIL                            |            |
| NonProduction | gcp-usm-nonprod-mon-gcve     | gcp-to-splunk              | gcp-to-splunk-subnet      | 172.31.0.0/24                  |            |
| NonProduction | gcp-usm-nonprod-mgmt-bkp     | NIL                        | NIL                       | NIL                            |            |
| NonProduction | gcp-usm-nonprod-net-vpc      | nonprod-net-0              | nonprod-landing-a-as1     | 172.30.24.0/24                 | Shared VPC |
|               |                              |                            | nonprod-security-a-as1    | 172.30.26.0/27                 | Shared VPC |
|               |                              | vpc-fw-mgt-npd-syd         | sn-fw-mgt-npd-syd         | 172.30.29.0/24                 | Shared VPC |
|               |                              | vpc-fw-private-npd-syd     | sn-fw-private-npd-syd     | 172.30.32.0/24                 | Shared VPC |
|               |                              | vpc-fw-public-npd-syd      | sn-fw-public-npd-syd      | 172.30.48.0/24, 172.30.52.0/22 | Shared VPC |
|               |                              | vpc-sdwanedge-inet-npd-syd | sn-sdwanedge-inet-npd-syd | 172.30.28.32/27                | Shared VPC |
|               |                              | vpc-sdwanedge-lan-npd-syd  | sn-sdwanedge-lan-npd-syd  | 172.30.28.96/27                | Shared VPC |
|               |                              | vpc-sdwanedge-mgt-npd-syd  | sn-sdwanedge-mgt-npd-syd  | 172.30.28.0/27                 | Shared VPC |
|               |                              | vpc-sdwanedge-vpn-npd-syd  | sn-sdwanedge-vpn-npd-syd  | 172.30.28.64/27                | Shared VPC |
| NonProduction | gcp-usm-nonprod-sec-core     | NIL                        | NIL                       | NIL                            |            |
| NonProduction | gcp-usm-nonprod-sec-ctm      | NIL                        | NIL                       | NIL                            |            |

### 1.2.3 GCVE

| Environment   | PrivateCloud    | Subnet              | CIDR             | Remark      |
|---------------|-----------------|---------------------|------------------|-------------|
| Production    | prod-gcve-02    | hcx-uplink          | ************/25  | management  |
|               |                 | nsxt-edge-uplink-1  | *********/28     | management  |
|               |                 | nsxt-edge-uplink-2  | **********/28    | management  |
|               |                 | nsxt-host-transport | *********/23     | management  |
|               |                 | service-1           | ***********/28   | userDefined |
|               |                 | service-2           | NIL              | userDefined |
|               |                 | service-3           | ************/26  | userDefined |
|               |                 | service-4           | **********/24    | userDefined |
|               |                 | service-5           | **********/24    | userDefined |
|               |                 | system-management   | *********/22     | management  |
| NonProduction | nonprod-gcve-01 | hcx-uplink          | *************/25 | management  |
|               |                 | nsxt-edge-uplink-1  | **********/28    | management  |
|               |                 | nsxt-edge-uplink-2  | ***********/28   | management  |
|               |                 | nsxt-host-transport | **********/23    | management  |
|               |                 | service-1           | ***********/24   | userDefined |
|               |                 | service-2           | ***********/24   | userDefined |
|               |                 | service-3           | NIL              | userDefined |
|               |                 | service-4           | NIL              | userDefined |
|               |                 | service-5           | NIL              | userDefined |
|               |                 | system-management   | **********/22    | management  |

> **IMPORTANT:**  Above table shows the transport networks for VMware engine.
> **IMPORTANT:**  Below table shows the user networks for VMware engine.

| Environment   | Tier0       | Tier1-Gateway          | Tier1-Segment                | CIDR              | Remark |
|---------------|-------------|------------------------|------------------------------|-------------------|--------|
| Production    | Provider-LR | prod_dc2_dmz_gw        | Prod_DMZ_120                 | 192.168.36.0/24   |        |
|               |             | prod_dc3_dmz_gw        | Prod_DMZ_20                  | 192.168.35.254/24 |        |
|               |             | prod_gw                | commvault                    | ***********/28    |        |
|               |             |                        | DevOps_Testing_1991          | 172.20.254.0/24   |        |
|               |             |                        | Mgmt_Corp_Ext_DC2_DMZ_1100   | 192.168.31.0/24   |        |
|               |             |                        | Mgmt_Corp_Ext_DC3_DMZ_100    | 192.168.30.0/24   |        |
|               |             |                        | Mgmt_Corp_F5_45              | 10.3.45.0/24      |        |
|               |             |                        | Mgmt_Corp_F5_46              | 10.3.46.0/23      |        |
|               |             |                        | Mgmt_Crit_DB_40              | 10.3.40.0/23      |        |
|               |             |                        | Mgmt_Mgmt_DC2_1101           | 10.3.100.0/24     |        |
|               |             |                        | Mgmt_Mgmt_DC2_1103           | 10.7.102.0/24     |        |
|               |             |                        | Mgmt_Prod_10                 | 10.3.36.0/23      |        |
|               |             |                        | Mgmt_Prod_Shared_110         | 10.3.110.0/24     |        |
|               |             |                        | Mgmt_Srv_Mgmt_106            | 10.3.106.0/23     |        |
|               |             |                        | Mgmt_VDI_3030                | 10.30.30.0/23     |        |
|               |             |                        | Mgmt_VDI_3031                | 10.30.32.0/23     |        |
|               |             |                        | Mgmt_Voice_15                | 10.15.0.0/22      |        |
|               |             |                        | NetMgmtLAN1_2103             | 10.9.103.0/24     |        |
|               |             |                        | shared_svcs                  | 10.64.25.0/24     |        |
|               |             |                        | sp-VA-DC2-318                | 10.3.108.128/29   |        |
|               |             |                        | sp-VA-DC3-1318               | 10.3.108.136/29   |        |
| NonProduction | Provider-LR | nonprod_cmp_gw         | 5005-E0-ETS-SHARED-SERVICES  | 172.21.253.0/24   |        |
|               |             |                        | 5006-E0-SHARED-SERVICES      | 172.21.254.0/24   |        |
|               |             |                        | E0-BUBBLE-1-TEST             | 172.22.1.0/24     |        |
|               |             |                        | E0-BUBBLE-2-DMZ              | 172.22.2.0/24     |        |
|               |             |                        | E0-BUBBLE-3-CRIT             | 172.22.3.0/24     |        |
|               |             |                        | E0-BUBBLE-4-CORP             | 172.22.4.0/24     |        |
|               |             |                        | E0-BUBBLE-5-TEST             | 172.21.5.0/24     |        |
|               |             |                        | E0-BUBBLE-HORIZON-VDI        | 172.21.250.0/24   |        |
|               |             |                        | E0-PROD-2-CMP-RD             | 172.21.249.0/24   |        |
|               |             |                        | env-vdi                      | 172.20.250.0/24   |        |
|               |             |                        | nonprod labmgmt 1116         | 10.7.116.0/24     |        |
|               |             |                        | nonprod labmgmt 116          | 10.9.116.0/24     |        |
|               |             |                        | nonprod cmp SVT              | 172.22.6.0/24     |        |
|               |             |                        | nonprod-cmp-avi-lb           | 172.22.7.0/24     |        |
|               |             |                        | nonprod-cmp-svt-tool         | 172.22.8.0/24     |        |
|               |             | nonprod_dmz_gw         | nonprod-dmz                  | 172.20.243.0/22   |        |
|               |             | nonprod_lab00_gw       | DevTest-lab0-corp-ltm-1506   | 172.20.6.0/24     |        |
|               |             |                        | DevTest-lab0-corp-srv-1502   | 172.20.2.0/24     |        |
|               |             |                        | DevTest-lab0-corp-wks-1503   | 172.20.3.0/24     |        |
|               |             |                        | DevTest-lab0-corp-wks-2-1509 | 172.20.11.0/23    |        |
|               |             |                        | DevTest-lab0-crit-db-1501    | 172.20.1.0/24     |        |
|               |             |                        | DevTest-lab0-dmz-1507        | 172.20.7.0/24     |        |
|               |             |                        | DevTest-lab0-mgt-1508        | 172.20.8.0/24     |        |
|               |             | nonprod_lab03_gw       | DevTest-lab3-corp-ltm-1536   | 172.20.36.0/24    |        |
|               |             |                        | DevTest-lab3-corp-srv-1532   | 172.20.32.0/24    |        |
|               |             |                        | DevTest-lab3-corp-wks-1533   | 172.20.33.0/24    |        |
|               |             |                        | DevTest-lab3-crit-db-1531    | 172.20.31.0/24    |        |
|               |             |                        | DevTest-lab3-dmz-1537        | 172.20.37.0/24    |        |
|               |             |                        | DevTest-lab3-mgt-1538        | 172.20.38.0/24    |        |
|               |             | nonprod_lab05_gw       | DevTest-lab5-corp-ltm-1556   | 172.20.56.254/24  |        |
|               |             |                        | DevTest-lab5-corp-srv-1552   | 172.20.52.254/24  |        |
|               |             |                        | DevTest-lab5-corp-wks-1553   | 172.20.53.254/24  |        |
|               |             |                        | DevTest-lab5-crit-db-1551    | 172.20.51.254/24  |        |
|               |             |                        | DevTest-lab5-dmz-1557        | 172.20.57.254/24  |        |
|               |             |                        | DevTest-lab5-mgt-1558        | 172.20.58.254/24  |        |
|               |             | nonprod_shared_svcs_gw | nonprod-shared-svcs          | 172.30.25.254/24  |        |

### 1.2.4 SDWAN

| Environment | LocationName | GroupName                     | CIDR           | Remark |
|-------------|--------------|-------------------------------|----------------|--------|
| Production  | Canberra     | officecorp-can-10.3.201.0_24  | 10.3.201.0/24  |        |
|             | Sydney       | officecorp-syd-10.3.202.0_24  | 10.3.202.0/24  |        |
|             | Brisbane     | officecorp-bne-10.3.204.0_24  | 10.3.204.0/24  |        |
|             | Adelaide     | officecorp-ade-10.3.205.0_24  | 10.3.205.0/24  |        |
|             | Perth        | officecorp-per-10.3.206.0_24  | 10.3.206.0/24  |        |
|             | Burwood      | officecorp-bur-10.3.207.0_24  | 10.3.207.0/24  |        |
|             | Melbourne    | officecorp-mel-10.30.10.0_23  | 10.30.10.0/23  |        |
|             |              | officecorp-mel-10.30.24.0_23  | 10.30.24.0/23  |        |
|             |              | officecorp-mel-10.30.34.0_23  | 10.30.34.0/23  |        |
|             |              | officecorp-mel-10.30.36.0_23  | 10.30.36.0/23  |        |
|             |              | officecorp-mel-10.30.38.0_23  | 10.30.38.0/23  |        |
|             |              | officecorp-mel-10.30.40.0_23  | 10.30.40.0/23  |        |
|             |              | officecorp-mel-10.30.100.0_23 | 10.30.100.0/23 |        |
|             |              | officecorp-mel-10.30.102.0_23 | 10.30.102.0/23 |        |
|             |              | officecorp-mel-10.30.104.0_23 | 10.30.104.0/23 |        |
|             |              | officecorp-mel-10.30.112.0_21 | 10.30.112.0/21 |        |
|             |              | officecorp-mel-10.30.128.0_21 | 10.30.128.0/21 |        |
|             |              | officecorp-mel-10.30.136.0_23 | 10.30.136.0/23 |        |
|             |              | officecorp-mel-***********_23 | ***********/23 |        |
|             | OnCampus     | officecorp-oc-**********_19   | **********/19  |        |

### 1.2.5 VPN

| Environment | CIDR          | DNSServer            | Remark     |
|-------------|---------------|----------------------|------------|
| Production  | **********/20 | *********, ********* | For Global |

## 2. IaC Repository Reference
### 2.1 Azure DevOps Organization Structure

| Level        | Description                                  | Examples                                                                                                                                                                                                                                                                                                                                                                           |
|--------------|----------------------------------------------|------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| Organization | Top-level container for all projects         | Unisuper(for app team), Unisuper-Infrastructure(for platform team)                                                                                                                                                                                                                                                                                                                 |
| Project      | Container for landing zone repositories      | ACS-Integration, Agentic-AI, Ansible-Modules, Cleanroom, Connectivity, Core-Administration, Corporate, Customer-Communications, Database-Administration, Digital, Employer, GCP, GCVE, Identity, Infratstructure-and-Cloud, Integration, Investments, Management, MDP-Infra, Messaging, Palo-Alto-Firewall-Management, Powershell, Terraform-Import, Terraform-Modules, usm-legacy |
| Repository   | Individual code repositories within projects | azure-connectivity-non-production, azure-connectivity-production, azure-usm-non-production-network, azure-usm-production-network, azure-next-gen-firewall-vwan etc                                                                                                                                                                                                                 |

> **IMPORTANT:**  For the PoV, we will be only interested in Unisuper-Infrastructure organization, Connectivity project and above mentioned repositories only.

### 2.2 Project to Landing Zone Mapping

| Azure DevOps Project          | Landing Zone                       | Environment               | Purpose                                                 | Remark                                                                                    |
|-------------------------------|------------------------------------|---------------------------|---------------------------------------------------------|-------------------------------------------------------------------------------------------|
| Connectivity                  | Connectivity                       | Production, NonProduction | Core network services, ExpressRoute, SDWAN, Firewall    | All networking code are residing in this project including other subscription             |
| Identity                      | Identity                           | Production, NonProduction | Identity, Cyberark                                      | All services from IAM team                                                                |
| Management                    | Management                         | Production, NonProduction | Monitoring, Logging, Security services, DevOps services | Cribl, Orca, ADO agent, Intune, Splunk                                                    |
| Core-Administration           | Core-Administration                | Production, NonProduction | Landing zone                                            | RPA-Power Automate                                                                        |
| Corporate                     | Corporate                          | Production, NonProduction | Landing zone                                            | Abacus, filenote, sailpoint                                                               |
| Database-Administration       | NIL                                | Production, NonProduction | NIL                                                     | All services from DB team                                                                 |
| Digital                       | Digital                            | Production, NonProduction | Landing zone                                            | Sitecore, Advise Operations Portal, Consultative Comittee Website, Member Services Portal |
| Employer                      | Employer                           | Production, NonProduction | Landing zone                                            | NIL                                                                                       |
| GCP                           | Google Cloud                       | Production, NonProduction | Project, IAM, VM, storage, firewall                     | All services from GCP                                                                     |
| GCVE                          | Google Cloud                       | Production, NonProduction | vSphere, NSXT, VM                                       | All services from GCVE                                                                    |
| Infrastructure-and-Cloud      | NIL                                | Production, NonProduction | Legacy                                                  | Some legacy automation code/scripts                                                       |
| Integration                   | USM Production, USM Non-Production | Production, NonProduction | Legacy                                                  | iPaaS has not been migrated to new landing zone yet                                       |
| Investments                   | Investments                        | Production, NonProduction | Landing zone                                            | All services from Investment team                                                         |
| MDP-Infra                     | Data-Management                    | Production, NonProduction | Landing zone                                            | All services from Data team                                                               |
| Messaging                     | Messaging                          | Production, NonProduction | Landing zone                                            | NIL                                                                                       |
| Palo-Alto-Firewall-Management | NIL                                | Production, NonProduction | Palo-Alto firewalls                                     | All firewalls and its management device from Palo Alto                                    |
| Powershell                    | NIL                                | Production, NonProduction | Custom scripts                                          | All powershell custom scripts                                                             |
| Terraform-Import              | USM Production, USM Non-Production | Production, NonProduction | Legacy                                                  | All legacy Azure resources that yet to import into Terraform                              |
| Terraform-Modules             | NIL                                | NIL                       | Terraform modules                                       | All in-house developed Terraform modules                                                  |
| usm-legacy                    | USM Production, USM Non-Production | Production, NonProduction | Legacy                                                  | All legacy Azure resources that yet to import into Terraform                              |

> **IMPORTANT:**  Only Unisuper-Infrastructure organization is mapped above.

### 2.3 Connectivity Project Repositories

> **IMPORTANT:**  All Azure connectivity resources for every landing zone are managed in the connectivity ADO project.

| Repository                                            | Description                                  | Environmet                | Remark                                                |
|-------------------------------------------------------|----------------------------------------------|---------------------------|-------------------------------------------------------|
| ado-aks-agent                                         | for ado agent build on aks platform          | Production, NonProduction | will be moved to management after this                |
| az-clearpass                                          | for clearpass policy manager on connectivity | Production                |                                                       |
| azure-acs-data-archive-production-network             | imported for legacy networking components    | Production                |                                                       |
| azure-acs-integration-production-network              | imported for legacy networking components    | Production                |                                                       |
| azure-connectivity-non-production                     | for all landing zones networking components  | Production                | excluding the imported repositories                   |
| azure-connectivity-production                         | for all landing zones networking components  | NonProduction             | excluding the imported repositories                   |
| azure-corporate-non-production-network                | imported for legacy networking components    | NonProduction             |                                                       |
| azure-corporate-production-network                    | imported for legacy networking components    | Production                |                                                       |
| azure-mergers-and-acquisitions-non-production-network | imported for legacy networking components    | NonProduction             |                                                       |
| azure-next-gen-firewall-vwan                          | for virtual wan and peerings                 | Production, NonProduction |                                                       |
| azure-non-prod-external-lb                            | for external load balancer                   | NonProduction             |                                                       |
| azure-pan-panorama                                    | for panorama as firewall managemement vm     | Production                |                                                       |
| azure-pan-vm-series                                   | not in use                                   | Production, NonProduction | vm series deployed in azure connectivity repo instead |
| azure-sdwan-edge                                      | for aruba sdwan edge build                   | Production, NonProduction |                                                       |
| azure-usm-non-production-network                      | imported for legacy networking components    | Production                |                                                       |
| azure-usm-production-network                          | imported for legacy networking components    | NonProduction             |                                                       |
| google-pan-vm-series                                  | not in use                                   | Production, NonProduction | vm series deployed in GCP project                     |
| usm-production-network                                | obsolete                                     | NIL                       |                                                       |
| DONOTUSE_azure-connectivity-non-production            | obsolete                                     | NIL                       |                                                       |

### 2.4 Common Repository Structure

### 2.4.1 azure-connectivity-production repo

```
root/
├── melbourne
│   ├── hub
│   │   ├── backend.tf
│   │   ├── data.tf
│   │   ├── dns.tf
│   │   ├── express-route-circuit.tf
│   │   ├── locals.tf
│   │   ├── main.tf
│   │   ├── nsg.tf
│   │   ├── nva.tf
│   │   ├── pa_vmseries
│   │   │   ├── fw-compute.tf
│   │   │   ├── fw-net.tf
│   │   │   ├── internal-lb.tf
│   │   │   ├── main.tf
│   │   │   ├── outputs.tf
│   │   │   ├── providers.tf_nodeploy
│   │   │   ├── README.md
│   │   │   └── variables.tf
│   │   ├── peering.tf
│   │   ├── pipeline.yaml
│   │   ├── provider.tf
│   │   ├── README.md
│   │   ├── terraform.lock.hcl
│   │   ├── terraform.tfvars
│   │   ├── tobedeleted_peering-acs.tf
│   │   ├── variables.tf
│   │   ├── virtual_network_gateway.tf
│   │   └── vms-fwplatform.tf
│   └── spoke
│       ├── backend.tf
│       ├── config
│       │   ├── connectivity-production
│       │   │   ├── nsg.yaml
│       │   │   ├── resource_group.yaml
│       │   │   ├── route_table.yaml
│       │   │   ├── subnet.yaml
│       │   │   └── vnet.yaml
│       │   ├── corporate-production
│       │   │   ├── non_standard_resource_group.yaml
│       │   │   ├── non_standard_vnet.yaml
│       │   │   ├── nsg.yaml
│       │   │   ├── route_table.yaml
│       │   │   └── subnet.yaml
│       │   ├── data-management-production
│       │   │   ├── nsg.yaml
│       │   │   ├── resource_group.yaml
│       │   │   ├── route_table.yaml
│       │   │   ├── subnet.yaml
│       │   │   └── vnet.yaml
│       │   ├── digital-production
│       │   │   ├── nsg.yaml
│       │   │   ├── resource_group.yaml
│       │   │   ├── route_table.yaml
│       │   │   ├── subnet.yaml
│       │   │   └── vnet.yaml
│       │   ├── identity-production
│       │   │   ├── nsg.yaml
│       │   │   ├── resource_group.yaml
│       │   │   ├── route_table.yaml
│       │   │   ├── subnet.yaml
│       │   │   └── vnet.yaml
│       │   └── management-production
│       │       ├── nsg.yaml
│       │       ├── resource_group.yaml
│       │       ├── route_table.yaml
│       │       ├── subnet.yaml
│       │       └── vnet.yaml
│       ├── data.tf
│       ├── import.tf
│       ├── locals.tf
│       ├── main.tf
│       ├── output.tf
│       ├── pipeline.yaml
│       ├── provider.tf
│       └── variables.tf
├── modules
│   └── config
│       ├── locals.tf
│       ├── outputs.tf
│       └── variables.tf
├── README.md
└── sydney
    ├── hub
    │   ├── backend.tf
    │   ├── data.tf
    │   ├── express-route-circuit.tf
    │   ├── locals.tf
    │   ├── main.tf
    │   ├── nsg.tf
    │   ├── pa_vmseries
    │   │   ├── fw-compute.tf
    │   │   ├── fw-net.tf
    │   │   ├── internal-lb.tf
    │   │   ├── main.tf
    │   │   ├── outputs.tf
    │   │   ├── providers.tf_nodeploy
    │   │   ├── README.md
    │   │   └── variables.tf
    │   ├── peering.tf
    │   ├── pipeline.yaml
    │   ├── provider.tf
    │   ├── README.md
    │   ├── terraform.lock.hcl
    │   ├── terraform.tfvars
    │   ├── variables.tf
    │   ├── virtual_network_gateway.tf
    │   └── vms-fwplatform.tf
    └── spoke
        ├── backend.tf
        ├── config
        │   ├── core-administration-production
        │   │   ├── nsg.yaml
        │   │   ├── resource_group.yaml
        │   │   ├── route_table.yaml
        │   │   ├── subnet.yaml
        │   │   └── vnet.yaml
        │   ├── data-management-production
        │   │   ├── nsg.yaml
        │   │   ├── resource_group.yaml
        │   │   ├── route_table.yaml
        │   │   ├── subnet.yaml
        │   │   └── vnet.yaml
        │   ├── digital-production
        │   │   ├── nsg.yaml
        │   │   ├── resource_group.yaml
        │   │   ├── route_table.yaml
        │   │   ├── subnet.yaml
        │   │   └── vnet.yaml
        │   ├── identity-production
        │   │   ├── nsg.yaml
        │   │   ├── resource_group.yaml
        │   │   ├── route_table.yaml
        │   │   ├── subnet.yaml
        │   │   └── vnet.yaml
        │   ├── investments-production
        │   │   ├── nsg.yaml
        │   │   ├── resource_group.yaml
        │   │   ├── route_table.yaml
        │   │   ├── subnet.yaml
        │   │   └── vnet.yaml
        │   └── management-production
        │       ├── nsg.yaml
        │       ├── resource_group.yaml
        │       ├── route_table.yaml
        │       ├── subnet.yaml
        │       └── vnet.yaml
        ├── data.tf
        ├── import.tf
        ├── locals.tf
        ├── main.tf
        ├── output.tf
        ├── pipeline.yaml
        ├── provider.tf
        └── variables.tf
```

### 2.4.2 azure-connectivity-non-production repo

```
root/
├── hub
│   ├── backend.tf
│   ├── config
│   │   ├── nsg.yaml
│   │   ├── resource_group.yaml
│   │   ├── route_table.yaml
│   │   ├── subnet.yaml
│   │   └── vnet.yaml
│   ├── data.tf
│   ├── dns.tf
│   ├── import.tf
│   ├── main.tf
│   ├── modules
│   │   └── config
│   │       ├── locals.tf
│   │       ├── outputs.tf
│   │       └── variables.tf
│   ├── output.tf
│   ├── pa_vmseries
│   │   ├── fw-compute.tf
│   │   ├── fw-net.tf
│   │   ├── internal-lb.tf
│   │   ├── locals.tf
│   │   ├── main.tf
│   │   ├── outputs.tf
│   │   ├── providers.tf_nodeploy
│   │   ├── README.md
│   │   └── variables.tf
│   ├── peering.tf
│   ├── pipeline.yaml
│   ├── provider.tf
│   ├── README.md
│   └── vms-fwplatform.tf
├── modules
│   └── config
│       ├── locals.tf
│       ├── outputs.tf
│       └── variables.tf
├── README.md
└── spoke
    ├── backend.tf
    ├── config
    │   ├── core-administration-non-production
    │   │   ├── nsg.yaml
    │   │   ├── resource_group.yaml
    │   │   ├── route_table.yaml
    │   │   ├── subnet.yaml
    │   │   └── vnet.yaml
    │   ├── corporate-non-production
    │   │   ├── non_standard_resource_group.yaml
    │   │   ├── non_standard_route_table.yaml
    │   │   ├── non_standard_vnet.yaml
    │   │   ├── nsg.yaml
    │   │   └── subnet.yaml
    │   ├── data-management-non-production
    │   │   ├── nsg.yaml
    │   │   ├── resource_group.yaml
    │   │   ├── route_table.yaml
    │   │   ├── subnet.yaml
    │   │   └── vnet.yaml
    │   ├── digital-non-production
    │   │   ├── nsg.yaml
    │   │   ├── resource_group.yaml
    │   │   ├── route_table.yaml
    │   │   ├── subnet.yaml
    │   │   └── vnet.yaml
    │   ├── identity-non-production
    │   │   ├── nsg.yaml
    │   │   ├── resource_group.yaml
    │   │   ├── route_table.yaml
    │   │   ├── subnet.yaml
    │   │   └── vnet.yaml
    │   ├── investments-non-production
    │   │   ├── nsg.yaml
    │   │   ├── resource_group.yaml
    │   │   ├── route_table.yaml
    │   │   ├── subnet.yaml
    │   │   └── vnet.yaml
    │   └── management-non-production
    │       ├── nsg.yaml
    │       ├── resource_group.yaml
    │       ├── route_table.yaml
    │       ├── subnet.yaml
    │       └── vnet.yaml
    ├── data.tf
    ├── import.tf
    ├── locals.tf
    ├── main.tf
    ├── modules
    │   └── config
    │       ├── locals.tf
    │       ├── outputs.tf
    │       └── variables.tf
    ├── output.tf
    ├── pipeline.yaml
    ├── provider.tf
    └── variables.tf
```

### 2.4.3 azure-next-gen-firewall-vwan repo

```
/root
├── nonprod-mel
│   ├── cloudngfw.tf
│   ├── connect-erc.tf
│   ├── connect-sdwan.tf
│   ├── main.tf
│   ├── providers.tf
│   ├── README.md
│   ├── spoke-peerings.tf
│   ├── variables.tf
│   └── vwan.tf
├── prod-mel
│   ├── cloudngfw.tf
│   ├── connect-erc.tf
│   ├── connect-sdwan.tf
│   ├── main.tf
│   ├── providers.tf
│   ├── README.md
│   ├── spoke-peerings.tf
│   ├── variables.tf
│   └── vwan.tf
├── prod-syd
│   ├── cloudngfw.tf
│   ├── connect-erc.tf
│   ├── connect-sdwan.tf
│   ├── main.tf
│   ├── providers.tf
│   ├── README.md
│   ├── spoke-peerings.tf
│   ├── spoke-peerings.tf_nodeploy
│   ├── variables.tf
│   └── vwan.tf
└── README.md
```

### 2.4.4 azure-usm-production-network

```
/root
├── NSGs
│   ├── convert2csv.ps1
│   ├── mc_rg-prd-mel-integ-platform_aks-prd-mel-dde-integ_australiasoutheast
│   │   ├── aztfexportResourceMapping.json
│   │   ├── main.aztfexport.tf
│   │   ├── provider.tf
│   │   └── terraform.tf
│   ├── mc_rg-prd-syd-integ-platform_aks-prd-syd-cot-integ_australiaeast
│   │   ├── aztfexportResourceMapping.json
│   │   ├── main.aztfexport.tf
│   │   ├── provider.tf
│   │   └── terraform.tf
│   ├── output.csv
│   ├── rg-connectivity-prd-01
│   │   ├── aztfexportResourceMapping.json
│   │   ├── main.aztfexport.tf
│   │   ├── provider.tf
│   │   └── terraform.tf
│   ├── rg-dre-inv
│   │   ├── aztfexportResourceMapping.json
│   │   ├── main.aztfexport.tf
│   │   ├── provider.tf
│   │   └── terraform.tf
│   ├── rg-prd-commvault
│   │   ├── aztfexportResourceMapping.json
│   │   ├── aztfexportSkippedResources.txt
│   │   ├── main.aztfexport.tf
│   │   ├── provider.tf
│   │   └── terraform.tf
│   ├── rg-prd-devops
│   │   ├── aztfexportResourceMapping.json
│   │   ├── main.aztfexport.tf
│   │   ├── provider.tf
│   │   └── terraform.tf
│   ├── rg-prd-enterprise-core-monitoring
│   │   ├── aztfexportResourceMapping.json
│   │   ├── main.aztfexport.tf
│   │   ├── provider.tf
│   │   └── terraform.tf
│   ├── rg-prd-firewall
│   │   ├── aztfexportResourceMapping.json
│   │   ├── main.aztfexport.tf
│   │   ├── provider.tf
│   │   ├── terraform.tf
│   │   └── tfduplicates.csv
│   ├── rg-prd-infosec
│   │   ├── aztfexportResourceMapping.json
│   │   ├── main.aztfexport.tf
│   │   ├── provider.tf
│   │   └── terraform.tf
│   ├── rg-prd-inv
│   │   ├── aztfexportResourceMapping.json
│   │   ├── main.aztfexport.tf
│   │   ├── provider.tf
│   │   └── terraform.tf
│   ├── rg-prd-mel-integ-bunker
│   │   ├── aztfexportResourceMapping.json
│   │   ├── main.aztfexport.tf
│   │   ├── provider.tf
│   │   └── terraform.tf
│   ├── rg-prd-mel-rpd7
│   │   ├── aztfexportResourceMapping.json
│   │   ├── main.aztfexport.tf
│   │   ├── provider.tf
│   │   └── terraform.tf
│   ├── rg-prd-network
│   │   ├── aztfexportResourceMapping.json
│   │   ├── main.aztfexport.tf
│   │   ├── provider.tf
│   │   └── terraform.tf
│   ├── rg-prd-scusm
│   │   ├── aztfexportResourceMapping.json
│   │   ├── main.aztfexport.tf
│   │   ├── provider.tf
│   │   └── terraform.tf
│   ├── rg-prd-syd-integ-bunker
│   │   ├── aztfexportResourceMapping.json
│   │   ├── main.aztfexport.tf
│   │   ├── provider.tf
│   │   └── terraform.tf
│   ├── rg-prd-syd-snyk
│   │   ├── aztfexportResourceMapping.json
│   │   ├── main.aztfexport.tf
│   │   ├── provider.tf
│   │   ├── README.md
│   │   └── terraform.tf
│   ├── rg-shd-coreservices
│   │   ├── aztfexportResourceMapping.json
│   │   ├── main.aztfexport.tf
│   │   ├── provider.tf
│   │   └── terraform.tf
│   └── rg-shd-network
│       ├── aztfexportResourceMapping.json
│       ├── main.aztfexport.tf
│       ├── provider.tf
│       └── terraform.tf
├── provider.aztfexport.tf
├── README.md
├── RouteTables
│   ├── rg-dre-inv
│   │   ├── aztfexportResourceMapping.json
│   │   ├── main.aztfexport.tf
│   │   ├── provider.tf
│   │   └── terraform.tf
│   ├── rg-prd-firewall
│   │   ├── aztfexportResourceMapping.json
│   │   ├── main.aztfexport.tf
│   │   ├── provider.tf
│   │   └── terraform.tf
│   ├── rg-prd-inv
│   │   ├── aztfexportResourceMapping.json
│   │   ├── main.aztfexport.tf
│   │   ├── provider.tf
│   │   └── terraform.tf
│   ├── rg-prd-network
│   │   ├── aztfexportResourceMapping.json
│   │   ├── main.aztfexport.tf
│   │   ├── provider.tf
│   │   └── terraform.tf
│   ├── rg-prd-syd-snyk
│   │   ├── aztfexportResourceMapping.json
│   │   ├── main.aztfexport.tf
│   │   ├── provider.tf
│   │   └── terraform.tf
│   └── rg-shd-network
│       ├── aztfexportResourceMapping.json
│       ├── main.aztfexport.tf
│       ├── provider.tf
│       └── terraform.tf
├── terraform.aztfexport.tf
└── VNETs
    ├── rg-connectivity-prd-01
    │   ├── aztfexportResourceMapping.json
    │   ├── main.aztfexport.tf
    │   ├── provider.tf
    │   └── terraform.tf
    ├── rg-dre-inv
    │   ├── aztfexportResourceMapping.json
    │   ├── main.aztfexport.tf
    │   ├── provider.tf
    │   └── terraform.tf
    ├── rg-prd-inv
    │   ├── aztfexportResourceMapping.json
    │   ├── main.aztfexport.tf
    │   ├── provider.tf
    │   └── terraform.tf
    ├── rg-prd-network
    │   ├── aztfexportResourceMapping.json
    │   ├── data.tf
    │   ├── main.aztfexport.tf
    │   ├── provider.tf
    │   └── terraform.tf
    ├── rg-prd-syd-snyk
    │   ├── aztfexportResourceMapping.json
    │   ├── main.aztfexport.tf
    │   ├── provider.tf
    │   └── terraform.tf
    ├── rg-shd-network
    │   ├── aztfexportResourceMapping.json
    │   ├── main.aztfexport.tf
    │   ├── provider.tf
    │   └── terraform.tf
    └── rg-usm-cvbackup-prd-syd
        ├── aztfexportResourceMapping.json
        ├── main.aztfexport.tf
        ├── provider.tf
        └── terraform.tf
```

### 2.4.5 azure-next-gen-firewall-vwan repo

```
/root
├── NSGs
│   ├── rg-connectivity-npd-01
│   │   ├── aztfexportResourceMapping.json
│   │   ├── main.aztfexport.tf
│   │   ├── provider.tf
│   │   └── terraform.tf
│   ├── rg-dev-mel-engineeringtest
│   │   ├── aztfexportResourceMapping.json
│   │   ├── main.aztfexport.tf
│   │   ├── provider.tf
│   │   └── terraform.tf
│   ├── rg-dev-mel-integ-bunker
│   │   ├── aztfexportResourceMapping.json
│   │   ├── main.aztfexport.tf
│   │   ├── provider.tf
│   │   └── terraform.tf
│   ├── rg-dev-scpoc-mol
│   │   ├── aztfexportResourceMapping.json
│   │   ├── main.aztfexport.tf
│   │   ├── provider.tf
│   │   └── terraform.tf
│   ├── rg-npd-commvault
│   │   ├── aztfexportResourceMapping.json
│   │   ├── main.aztfexport.tf
│   │   ├── provider.tf
│   │   └── terraform.tf
│   ├── rg-npd-devops
│   │   ├── aztfexportResourceMapping.json
│   │   ├── main.aztfexport.tf
│   │   ├── provider.tf
│   │   └── terraform.tf
│   ├── rg-npd-enterprise-core-monitoring
│   │   ├── aztfexportResourceMapping.json
│   │   ├── main.aztfexport.tf
│   │   ├── provider.tf
│   │   └── terraform.tf
│   ├── rg-npd-firewall
│   │   ├── aztfexportResourceMapping.json
│   │   ├── main.aztfexport.tf
│   │   ├── provider.tf
│   │   └── terraform.tf
│   ├── rg-npd-infosec
│   │   ├── aztfexportResourceMapping.json
│   │   ├── main.aztfexport.tf
│   │   ├── provider.tf
│   │   └── terraform.tf
│   ├── rg-npd-mel-appd-01
│   │   ├── aztfexportResourceMapping.json
│   │   ├── main.aztfexport.tf
│   │   ├── provider.tf
│   │   └── terraform.tf
│   ├── rg-npd-mel-devenv-integ
│   │   ├── aztfexportResourceMapping.json
│   │   ├── main.aztfexport.tf
│   │   ├── provider.tf
│   │   └── terraform.tf
│   ├── rg-npd-mel-scperf-01
│   │   ├── aztfexportResourceMapping.json
│   │   ├── main.aztfexport.tf
│   │   ├── provider.tf
│   │   └── terraform.tf
│   ├── rg-npd-network
│   │   ├── aztfexportResourceMapping.json
│   │   ├── main.aztfexport.tf
│   │   ├── provider.tf
│   │   └── terraform.tf
│   ├── rg-npd-plananddesign
│   │   ├── aztfexportResourceMapping.json
│   │   ├── main.aztfexport.tf
│   │   ├── provider.tf
│   │   └── terraform.tf
│   ├── rg-npd-sitecore1
│   │   ├── aztfexportResourceMapping.json
│   │   ├── main.aztfexport.tf
│   │   ├── provider.tf
│   │   └── terraform.tf
│   └── rg-tst-qmv
│       ├── aztfexportResourceMapping.json
│       ├── main.aztfexport.tf
│       ├── provider.tf
│       └── terraform.tf
├── provider.aztfexport.tf
├── README.md
├── RouteTables
│   ├── rg-npd-coreservices
│   │   ├── aztfexportResourceMapping.json
│   │   ├── main.aztfexport.tf
│   │   ├── provider.tf
│   │   └── terraform.tf
│   ├── rg-npd-firewall
│   │   ├── aztfexportResourceMapping.json
│   │   ├── main.aztfexport.tf
│   │   ├── provider.tf
│   │   └── terraform.tf
│   ├── rg-npd-mel-devenv-integ
│   │   ├── aztfexportResourceMapping.json
│   │   ├── main.aztfexport.tf
│   │   ├── provider.tf
│   │   └── terraform.tf
│   └── rg-npd-network
│       ├── aztfexportResourceMapping.json
│       ├── main.aztfexport.tf
│       ├── provider.tf
│       └── terraform.tf
├── terraform.aztfexport.tf
└── Vnets
    ├── rg-DEV-MEL-EngineeringTest
    │   ├── aztfexportResourceMapping.json
    │   ├── main.aztfexport.tf
    │   ├── provider.tf
    │   └── terraform.tf
    ├── rg-npd-mel-devenv-integ
    │   ├── aztfexportResourceMapping.json
    │   ├── main.aztfexport.tf
    │   ├── provider.tf
    │   └── terraform.tf
    └── rg-npd-network
        ├── aztfexportResourceMapping.json
        ├── main.aztfexport.tf
        ├── provider.tf
        └── terraform.tf
```
> **IMPORTANT:** Ignore the JSON file as it was generated by azcli for import purposes

#### 2.4.4 Pipeline Triggers
Within IaC repositories, some are triggered via Azure DevOps pipelines while others are directly webhooked onto Terraform Cloud. All code merges through Pull Requests will trigger pipeline runs.
The trigger mechanism selection is based on these criteria:
- Repositories requiring complex approval workflows use Azure DevOps pipelines
- Repositories with standardized infrastructure patterns use direct Terraform Cloud webhooks

| PipelineType | Repositories                                                                                                                                                                                                                                                                                                                                                                           |
|--------------|----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| ADO          | ado-aks-agent, azure-connectivity-non-production, azure-corporate-non-production-network                                                                                                                                                                                                                                                                                               |
| TFC          | azure-acs-data-archive-production-network, azure-acs-integration-production-network, azure-corporate-non-production-network, azure-corporate-production-network, azure-mergers-and-acquisitions-non-production-network, azure-next-gen-firewall-vwan, azure-non-prod-external-lb, azure-pan-panorama, azure-sdwan-edge, azure-usm-non-production-network, azure-usm-production-network |

