import { Reference } from '@/actions/types';

const AttachmentItem = ({ name, content }: Reference) => { 

  const firstSlashIndex = name.indexOf('/');
  const repo_name = name.substring(0, firstSlashIndex);
  const file_path = name.substring(firstSlashIndex + 1);
  const url = `https://dev.azure.com/UniSuper-Infrastructure/Connectivity/_git/${repo_name}?path=/${file_path}`

  return (
  <div className="bg-gray-100 border border-gray-200 rounded-lg p-3 shadow-sm hover:shadow-md transition-shadow h-full">
    <a className="flex flex-col h-full" href={url} target="_blank">
      {/* Network Agent Icon and Description */}
      <div className="flex items-start space-x-2 mb-2">
        <div
          className="w-6 h-6 rounded-full flex items-center justify-center flex-shrink-0"
          style={{
            background:
              "radial-gradient(circle,rgb(35, 189, 195) 0%,rgb(37, 249, 221) 100%)",
          }}
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="12"
            height="12"
            viewBox="0 0 12 12"
            fill="none"
          >
            <path
              d="M7.4998 1.19922V2.99922C7.4998 3.33059 7.76843 3.59922 8.0998 3.59922H9.8998M4.1998 3.59922H5.3998M4.1998 5.39922H7.7998M4.1998 7.19922H7.7998M8.9998 2.09922C8.73276 1.86029 8.45566 1.57691 8.28072 1.39286C8.16431 1.27039 8.00348 1.19922 7.83451 1.19922H3.29966C2.63692 1.19922 2.09966 1.73647 2.09966 2.39921L2.09961 9.59918C2.09961 10.2619 2.63686 10.7992 3.2996 10.7992L8.69963 10.7992C9.36236 10.7992 9.89962 10.262 9.89963 9.59924L9.89979 3.23813C9.89979 3.08472 9.84127 2.93724 9.73479 2.82678C9.53789 2.62254 9.20909 2.28648 8.9998 2.09922Z"
              stroke="#A8FFF4"
              strokeLinecap="round" 
              strokeLinejoin="round"
            />
          </svg>
        </div>
        {/* Description */}
        <div className="flex-1 min-w-0">
          <p className="text-xs text-gray-600 leading-relaxed line-clamp-3 break-words">
            {content}
          </p>
        </div>
      </div>

      {/* Title - aligned with description (same left margin as description) */}
      <div className="flex items-start space-x-2">
        <div className="w-6 flex-shrink-0"></div>{" "}
        {/* Spacer to match icon width */}
        <div className="flex-1 min-w-0">
          <h4 className="text-sm font-medium text-gray-900 leading-tight truncate break-words">
            {name}
          </h4>
        </div>
      </div>
    </a>
  </div>
);}

export default AttachmentItem;
