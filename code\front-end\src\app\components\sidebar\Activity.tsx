import React from "react";
import Icon from "../../icons/Icon";

interface ActivitySection {
  title: string;
  count: number;
  isOpen: boolean;
}

interface ActivityProps {
  sections: ActivitySection[];
  onSectionToggle: (title: string) => void;
}

export default function Activity({ sections, onSectionToggle }: ActivityProps) {
  // Generate SVG with filled background for each section
  const getSectionSVG = (section: ActivitySection, index: number) => {
    const gradients = [
      // Blue gradient (Awaiting Input)
      {
        id: "paint0_linear_blue",
        fillColor: "#0E71F2",
        stops: [
          { offset: "0%", color: "#0E71F2" },
          { offset: "45%", color: "#0F50AA" },
          { offset: "100%", color: "#112C5C" }
        ]
      },
      // Light blue gradient (In Process)
      {
        id: "paint0_linear_lightblue",
        fillColor: "#82C9F0",
        stops: [
          { offset: "0%", color: "#82C9F0" },
          { offset: "45%", color: "#4398C7" },
          { offset: "100%", color: "#00527F" }
        ]
      },
      // Teal gradient (Assigned to)
      {
        id: "paint0_linear_teal",
        fillColor: "#00C1DA",
        stops: [
          { offset: "0%", color: "#00C1DA" },
          { offset: "45%", color: "#0095A7" },
          { offset: "100%", color: "#006977" }
        ]
      },
      // Gray gradient (Archive)
      {
        id: "paint0_linear_gray",
        fillColor: "#787878",
        stops: [
          { offset: "0%", color: "#787878" },
          { offset: "45%", color: "#565656" },
          { offset: "100%", color: "#343434" }
        ]
      }
    ];

    const gradient = gradients[index % gradients.length];

    // Exact Figma dimensions: width: 260, height: 160.75
    const svgContent = `
    <svg xmlns="http://www.w3.org/2000/svg" width="260" height="160.75" viewBox="0 0 260 160.75" fill="none">
      <rect x="0" y="0" width="260" height="160.75" fill="${gradient.fillColor}"/>
      <path d="M-38 -11.5V165H168.128C170.59 157.34 170.131 149.85 173.299 141.372C174.64 137.82 176.454 134.464 178.693 131.393C194.466 109.452 218.702 109.752 228.897 92.0151V91.9915C239.068 74.2393 226.464 53.6689 237.409 28.9804C241.119 20.6172 245.891 15.9861 250.79 11.9298C257.861 6.07825 265.199 1.42649 270 -11.5L-38 -11.5Z" fill="url(#${gradient.id})"/>
      <text x="20" y="40" text-anchor="start" fill="white" font-size="18" font-family="Arial, sans-serif" font-weight="900">${section.title}</text>
      <text x="15" y="140" text-anchor="start" fill="#FFF" font-size="100" font-family="Source Sans 3" font-style="normal" font-weight="100">${section.count}</text>
      <defs>
        <linearGradient id="${gradient.id}" x1="0" y1="160.75" x2="0" y2="0" gradientUnits="userSpaceOnUse">
          ${gradient.stops.map(stop => 
            `<stop offset="${stop.offset}" stop-color="${stop.color}"/>`
          ).join('')}
        </linearGradient>
      </defs>
    </svg>
  `;

  return `data:image/svg+xml,${encodeURIComponent(svgContent)}`;
};


  return (
    <div className="flex-1 overflow-y-auto">
      <div className="p-4">
        {/* Single Column Activity List with exact Figma specifications */}
        <div className="space-y-4"> {/* gap: 17px approximated to space-y-4 */}
          {sections.map((section, index) => (
            <button
              key={section.title}
              onClick={() => onSectionToggle(section.title)}
              className={`w-full overflow-hidden transition-all duration-200 relative ${
                section.isOpen 
                  ? "shadow-lg transform scale-105" 
                  : "shadow-md hover:shadow-lg hover:transform hover:scale-102"
              }`}
              style={{
                backgroundImage: `url("${getSectionSVG(section, index)}")`,
                backgroundSize: 'cover',
                backgroundPosition: 'center',
                backgroundRepeat: 'no-repeat',
                width: '260px',           // Exact Figma width
                height: '160.75px',       // Exact Figma height
                borderRadius: '4px',      // Exact Figma border-radius
                padding: '20px',          // Exact Figma padding
                minHeight: '160.75px'
              }}
            >
              {/* Optional: Add a subtle overlay for better interaction feedback */}
              <div className={`absolute inset-0 transition-opacity duration-200 ${
                section.isOpen 
                  ? "bg-white bg-opacity-10" 
                  : "bg-transparent hover:bg-white hover:bg-opacity-5"
              }`}></div>
            </button>
          ))}
        </div>
      </div>
    </div>
  );
}
