apiVersion: v1
kind: Secret
metadata:
  name: networking-agent-secret
  namespace: networking
type: Opaque
data:
  # networking secret
  AZURE_OPENAI_API_KEY: "ZWJpUFliWUxRTUxpTFZpc1RKUHJsOUxJOGE2M0htczVkdURRYkU2UThPVkZFYnJpcFVwckpRUUo5OUJGQUNMOTNOYVhKM3czQUFBQkFDT0dXbzNC"
  POSTGRESQL_URL: "************************************************************************************************************************************************************************"
  # MCP(mel-mcp) secret 
  AZURE_DEVOPS_PAT: "NGRRUER0YXBHVXVHb3hIRUpNdVhJdVpBcmhmUDRWVG9GRnlVbGQ1YklvT1Z6ZUNFZmt3Q0pRUUo5OUJHQUNBQUFBQTRGNWNQQUFBU0FaRE8zSmtD"
  ADO_PAT: "NGRRUER0YXBHVXVHb3hIRUpNdVhJdVpBcmhmUDRWVG9GRnlVbGQ1YklvT1Z6ZUNFZmt3Q0pRUUo5OUJHQUNBQUFBQTRGNWNQQUFBU0FaRE8zSmtD"
  AZURE_OPENAI_KEY: "ZWJpUFliWUxRTUxpTFZpc1RKUHJsOUxJOGE2M0htczVkdURRYkU2UThPVkZFYnJpcFVwckpRUUo5OUJGQUNMOTNOYVhKM3czQUFBQkFDT0dXbzNC"
  AZURE_SEARCH_API_KEY: "************************************************************************"
  # oauth2-proxy secret
  client-id: ODhkOTQyNzktNWIzNS00MDg1LTkyZjktOGI4MWNmNjc5NWFm
  client-secret: ********************************************************
  cookie-secret: ******************************************** # randomly generated secret for signing cookies



