variables:
    - group: terraform-cloud-team-token

trigger: none

stages:
    - stage: Plan
      dependsOn: []
      jobs:
          - job: terraform_plan
            displayName: Terraform Plan
            workspace:
                clean: all
            pool:
              name: $(VAR_AGENT_POOL)
            steps:
                - checkout: self
                  path: self
                  fetchDepth: 0
                  clean: true

                - task: AzureCLI@2
                  name: tfInit
                  displayName: Terraform Init
                  env:
                      TF_TOKEN_app_terraform_io: $(TF-TOKEN-app-terraform-io)
                      TF_IN_AUTOMATION: true
                      # TF_WORKSPACE: $(VAR_WORKSPACE_NAME)
                      ARM_SUBSCRIPTION_ID: $(ARM_SUBSCRIPTION_ID)
                  inputs:
                      azureSubscription: $(VAR_SERVICE_CONNECTION)
                      addSpnToEnvironment: true
                      scriptType: pscore
                      scriptLocation: inlineScript
                      workingDirectory: $(Agent.BuildDirectory)/self
                      inlineScript: |
                          terraform init -upgrade
                          terraform validate

                - task: AzureCL<PERSON>@2
                  name: tfPlan
                  displayName: Terraform Plan
                  env:
                      TF_TOKEN_app_terraform_io: $(TF-TOKEN-app-terraform-io)
                      TF_IN_AUTOMATION: true
                      # TF_WORKSPACE: $(VAR_WORKSPACE_NAME)
                      ARM_SUBSCRIPTION_ID: $(ARM_SUBSCRIPTION_ID)
                      TF_VAR_COSMOS_DB_PASSWORD: $(VAR_COSMOS_DB_PASSWORD)
                  inputs:
                      azureSubscription: $(VAR_SERVICE_CONNECTION)
                      addSpnToEnvironment: true
                      scriptType: pscore
                      scriptLocation: inlineScript
                      powerShellIgnoreLASTEXITCODE: true
                      workingDirectory: $(Agent.BuildDirectory)/self
                      inlineScript: |
                          terraform plan -detailed-exitcode
                          if ( $LASTEXITCODE -eq 2 ) {
                            Write-Host "##vso[task.setvariable variable=changedDetected;isOutput=true]true"
                          }

                - publish: $(Agent.BuildDirectory)/self
                  artifact: terraform_config

    - stage: Apply
      dependsOn: Plan
      condition: eq( dependencies.Plan.outputs['terraform_plan.tfPlan.changedDetected'], true )
      jobs:
          - deployment: terraform_apply
            displayName: Terraform Apply
            pool:
              name: $(VAR_AGENT_POOL)
            continueOnError: false
            environment: "infra-team-non-production-apply"
            timeoutInMinutes: 120
            strategy:
                runOnce:
                    deploy:
                        steps:
                            - download: current
                              artifact: terraform_config

                            - script: |
                                  echo "Change file permission on $(Agent.BuildDirectory)/terraform_config/.terraform/providers/"
                                  chmod -R +x "$(Agent.BuildDirectory)/terraform_config/.terraform/providers/"
                              displayName: TF Provider File Permission Change

                            - task: AzureCLI@2
                              displayName: Terraform Apply
                              env:
                                  TF_TOKEN_app_terraform_io: $(TF-TOKEN-app-terraform-io)
                                  TF_IN_AUTOMATION: true
                                  # TF_WORKSPACE: $(VAR_WORKSPACE_NAME)
                                  ARM_SUBSCRIPTION_ID: $(ARM_SUBSCRIPTION_ID)
                                  TF_VAR_COSMOS_DB_PASSWORD: $(VAR_COSMOS_DB_PASSWORD)
                              inputs:
                                  azureSubscription: $(VAR_SERVICE_CONNECTION)
                                  addSpnToEnvironment: true
                                  scriptType: pscore
                                  scriptLocation: inlineScript
                                  workingDirectory: $(Agent.BuildDirectory)/terraform_config
                                  inlineScript: |
                                      terraform apply -auto-approve