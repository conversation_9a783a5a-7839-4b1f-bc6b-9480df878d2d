
# Create the Linux App Service Plan
resource "azurerm_service_plan" "mcp-asp" {
  name                = "${module.naming.app_service_plan_name}-mcp-asp"
  location            = module.resource_group.location
  resource_group_name = module.resource_group.name
  os_type             = "Linux"
  sku_name            = "S1"

  tags = local.common_tags
}
# resource "azurerm_application_insights" "mcp-appi" {
#   name                = "${module.naming.application_insights_name}-mcp-appi"
#   resource_group_name = module.resource_group.name
#   location            = module.resource_group.location
#   application_type    = "web"

#   tags = local.common_tags
#   lifecycle {
#     ignore_changes = [
#       workspace_id
#     ]
#   }
# }
# Create the web app, pass in the App Service Plan ID
resource "azurerm_linux_web_app" "mcp" {
  name                          = "${module.naming.web_app_name}-mcp"
  location                      = module.resource_group.location
  resource_group_name           = module.resource_group.name
  service_plan_id               = azurerm_service_plan.mcp-asp.id
  depends_on                    = [azurerm_service_plan.mcp-asp]
  https_only                    = false
  public_network_access_enabled = false
  virtual_network_subnet_id     = data.azurerm_subnet.app_snet.id
  identity {
    type = "SystemAssigned"
  }

  site_config {
    minimum_tls_version                    = "1.2"
    app_command_line                       = "cd networking_mcp-0.1.0 && pip install uv && uv run mcp run ./src/networking_mcp/__init__.py"
    application_stack {
      python_version = "3.11"
    }
  }
  tags = local.common_tags

  lifecycle {
    ignore_changes = [
      app_settings,
      site_config[0].app_command_line,
      logs
    ]
  }
}
# resource "azurerm_app_service_virtual_network_swift_connection" "mcp-vnet-integ" {
#   app_service_id = azurerm_linux_web_app.mcp.id
#   subnet_id      = data.azurerm_subnet.app_snet.id
#   depends_on = [
#     azurerm_service_plan.mcp-asp
#   ]
# }
