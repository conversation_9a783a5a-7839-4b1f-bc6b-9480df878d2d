[project]
name = "networking_agent"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
authors = [
    { name = "<PERSON>", email = "149848050+clau<PERSON><PERSON><PERSON><PERSON><PERSON>@users.noreply.github.com" }
]
requires-python = ">=3.11"
dependencies = [
    "click>=8.1.8",
    "langchain-mcp-adapters>=0.0.10",
    "langchain[openai]>=0.3.25",
    "langgraph>=0.4.1",
    "langgraph-checkpoint>=2.0.25",
    "langgraph-checkpoint-postgres>=2.0.21",
    "pip-audit>=2.9.0",
    "psycopg[binary]>=3.2.9",
    "python-dotenv>=1.1.0",
]

[project.scripts]
a2a = "a2a:main"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"
