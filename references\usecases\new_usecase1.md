🔹 User Input:
“I can’t SSH into my Azure VM "DEVASRPAUTL01-01" from Unisuper jumphost MEPUTIL20/MPPUTIL20. Can you check?”

🔹 Explanation & Analysis:
Agent finds source and destination IP by running NSLOOKUP
- source: MEPUTIL20-***********, MPPUTIL20-***********
- destination: NSLOOKUP failed for DEVASRPAUTL02-01 (have to run nslookup in non prod environment)

Missing DNS entry

🔹 Root Cause:
There is no DNS entry for the destination

🔹 Terraform Change Required:
Not required

🔹 File to Edit:
Not relevant

🔹 Updated File:
Not relevant

🔹 Further action:
Once the DNS entry has been added, agent then checks firewall rule
