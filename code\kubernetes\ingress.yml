apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: networking-ingress
  namespace: networking
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: /
    # cert-manager.io/cluster-issuer: "letsencrypt-prod"
    #nginx.ingress.kubernetes.io/ssl-redirect: "false"
    # OAuth2-Proxy annotations (HTTP URLs)
    #nginx.ingress.kubernetes.io/auth-url: "http://merlin.devops.lan/oauth2/auth"
    #nginx.ingress.kubernetes.io/auth-signin: "http://merlin.devops.lan/oauth2/start?rd=http://$host$request_uri"
    #nginx.ingress.kubernetes.io/auth-response-headers: "X-Auth-Request-User,X-Auth-Request-Email"
spec:
  ingressClassName: nginx
  # tls:
  # - hosts:
  #   - devopsai-npd-mel-nwagt.devops.lan
    # - devopsai-npd-mel-a2a.devops.lan
    # - devopsai-npd-mel-mcp.devops.lan
    # secretName: networking-agent-tls
  rules:
  # - host: devopsai-npd-mel-nwagt.devops.lan # frontend-app
    - http:
        paths:
        - path: /
          pathType: Prefix
          backend:
            service:
              name: frontend-app-service
              port:
                number: 80
