export default function ThinkingIndicator() {
  return (
    <div className="bg-white">
      <div className="flex items-center space-x-2">
        <span className="text-xs font-medium">Network Agent is thinking</span>
        <div className="flex space-x-1">
          {[0, 150, 300].map((delay, index) => (
            <div
              key={index}
              className="w-2 h-2 rounded-full bg-gray-500 animate-bounce"
              style={{ animationDelay: `${delay}ms` }}
            />
          ))}
        </div>
      </div>
    </div>
  );
}
