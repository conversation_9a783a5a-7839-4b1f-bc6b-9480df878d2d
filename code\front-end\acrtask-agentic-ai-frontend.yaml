version: v1.1.0
alias:
  values:
    repo: agentic-ai
    container: frontend
    tag: test

steps:
  - id: build-on-acr
    build: --tag $Registry/$repo/$container:$ID .
    env:
      - DOCKER_BUILDKIT=1
    retries: 3
    retryDelay: 30
    startDelay: 10

  - id: push-to-acr
    push:
      - $Registry/$repo/$container:$ID

  - id: set-previous-tag
    cmd: |
      bash echo "update tags - Existing latest to previous"
      docker pull $Registry/$repo/$container:$tag-latest
      docker tag $Registry/$repo/$container:$tag-latest $Registry/$repo/$container:$tag-previous
      docker push $Registry/$repo/$container:$tag-previous
    ignoreErrors: true

  - id: set-latest-tag
    cmd: |
      bash echo "update tags - assign latest to fresh build"
      docker tag $Registry/$repo/$container:$ID $Registry/$repo/$container:$tag-latest
      docker push $Registry/$repo/$container:$tag-latest
    ignoreErrors: true
