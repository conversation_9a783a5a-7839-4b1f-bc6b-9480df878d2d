
# Use the official Node.js 22.16 image
FROM node:22 As builder
RUN apt-get install -y curl

FROM node:22-slim

ENV NODE_ENV production
ENV NEXT_TELEMETRY_DISABLED 1

# RUN npm install -g npm
# Set working directory
WORKDIR /app

# Copy dependency manifests first
COPY package*.json .

# Install dependencies
RUN npm install --omit=dev
# RUN npm install --force

# # Copy the rest of the application code
COPY . .

# Fix the permission issue
RUN chown -R node:node /app

RUN npm run build

# Expose the port your app runs on
EXPOSE 3000

#WORKDIR src

# Start the application
CMD ["npm", "run", "start"]