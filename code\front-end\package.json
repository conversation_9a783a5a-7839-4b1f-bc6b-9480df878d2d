{"name": "front-end", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@tailwindcss/postcss": "^4.1.10", "@types/react-syntax-highlighter": "^15.5.13", "autoprefixer": "^10.4.21", "cors": "^2.8.5", "express": "^5.1.0", "next": "^15.3.3", "prism-react-renderer": "^2.4.1", "react": "^19.0.0", "react-dom": "^19.0.0", "react-markdown": "^10.1.0", "remark-gfm": "^4.0.1", "uuid": "^11.1.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/node": "^22.15.21", "@types/react": "^19", "@types/react-dom": "^19", "@typescript-eslint/eslint-plugin": "^8.34.1", "@typescript-eslint/parser": "^8.34.1", "eslint": "^9.27.0", "eslint-config-next": "^15.3.3", "postcss": "^8.5.3", "tailwindcss": "^4.1.8", "typescript": "^5"}}