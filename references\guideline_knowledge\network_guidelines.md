### Introduction ###
This document provides technical guidelines for automated network troubleshooting and remediation across our multi-cloud hybrid architecture. These guidelines are designed to enable AI-based agents to diagnose, isolate, and resolve network issues by referencing Infrastructure-as-Code (IaC) repositories and implementing appropriate fixes.
The enterprise network spans multiple environments including Azure Cloud Adoption Framework (CAF) landing zones, Google Cloud Platform (GCP), Google Cloud VMware Engine (GCVE), Aruba SD-WAN, and Prisma VPN access. However, in this PoV phase, only Azure is considered. All network components are segregated into Production and Non-Production domains with distinct security postures and operational parameters.

### Troubleshooting Methodology ###
# General Approach #
When troubleshooting network issues, follow this sequence:
1. Identify the affected domain (Production or Non-Production)
2. Determine the affected cloud platform (Azure, GCP, GCVE) - It will be Azure in this PoV
3. Isolate the network component (VNet, VPC, SD-WAN, VPN) - It will be VNet in this PoV
4. Verify connectivity paths between source and destination - It will assume connectivity/routing has no issue
5. Check configuration against IaC connectivity repositories
6. Implement remediation based on use analysis

### Azure CAF Landing Zone Troubleshooting ###
# Connectivity Issues #
1. Hub-and-Spoke Connectivity
   - Verify NSG rules in both hub and spoke VNets
   - Check UDRs (User-Defined Routes) for correct next-hop configuration
   - Validate Azure Firewall rules for inter-spoke traffic
   - Ensure VNet peering is properly configured and active
2. ExpressRoute/VPN Connectivity
   - Verify BGP peering status between Virtual Network Gateway and SD-WAN
   - Check route propagation across connected networks
   - Validate IPSec tunnel status and crypto parameters
   - Ensure gateway subnet NSGs are properly configured
3. Private Endpoint Access
   - Verify private DNS zone integration
   - Check NSG rules for endpoint subnet
   - Validate service endpoint policies

# Common Remediation Action #
1. Update NSG rules via Terraform
2. Modify UDRs to correct routing issues
3. Reconfigure VNet peering settings
4. Reset VPN gateways if tunnel state is unstable
5. Update DNS configurations for name resolution issues

### Google Cloud Platform (GCP) Troubleshooting ###
# VPC and Connectivity Issues #
1. Shared VPC Connectivity
   - Verify IAM permissions for service projects
   - Check firewall rules at organization and project levels
   - Validate subnet ranges and route tables
   - Ensure proper service project attachment
2. Cloud Interconnect/VPN
   - Verify BGP session status with Cloud Router
   - Check route advertisement and learned routes
   - Validate VPN tunnel status and parameters
   - Ensure proper MTU configuration across paths
3. Internal Load Balancer Access
   - Verify health check configuration
   - Check backend service configuration
   - Validate forwarding rule settings

# Common Remediation Actions #
1. Update firewall rules via Terraform
2. Modify VPC routes to correct routing issues
3. Reconfigure Cloud Router BGP sessions
4. Reset VPN tunnels if connection is unstable
5. Update IAM permissions for cross-project resources

### GCVE (Google Cloud VMware Engine) Troubleshooting ###
# VMware Infrastructure Issues #
1. NSX-T Connectivity
   - Verify NSX-T segment configuration
   - Check Distributed Firewall (DFW) rules
   - Validate T0/T1 router configuration
   - Ensure proper VLAN to segment mapping
2. vSphere Networking
   - Verify distributed switch configuration
   - Check port group settings and VLAN tagging
   - Validate VMkernel interface configuration
   - Ensure proper uplink utilization

# Common Remediation Actions #
1. Update NSX-T DFW rules via Terraform
2. Modify segment configuration to correct connectivity
3. Reconfigure T0/T1 router settings
4. Reset NSX Edge nodes if service is unstable

### Aruba SD-WAN Troubleshooting ###
# Overlay Network Issues #
1. Path Selection and Quality
   - Verify SLA metrics (latency, jitter, packet loss)
   - Check path preference configuration
   - Validate QoS policy implementation
   - Ensure proper traffic steering rules
2. Branch Connectivity
   - Verify EdgeConnect appliance status
   - Check underlay connectivity (MPLS, Internet)
   - Validate orchestrator communication
   - Ensure proper template application

# Common Remediation Actions #
1. Update path selection policies via Orchestrator API
2. Modify QoS settings to improve performance
3. Reconfigure branch templates for consistency
4. Reset EdgeConnect appliances if service is unstable

### Prisma VPN Access Troubleshooting ###
# Remote Access Issues #
1. User Authentication
   - Verify identity provider integration (Azure AD, Google Workspace)
   - Check MFA configuration and status
   - Validate user group membership
   - Ensure proper certificate distribution
2. Connection Stability
   - Verify GlobalProtect gateway health
   - Check split-tunnel configuration
   - Validate IP pool allocation
   - Ensure proper route distribution
 
# Common Remediation Actions #
1. Update authentication policies via API
2. Modify split-tunnel configurations
3. Reconfigure IP pools for capacity issues
4. Reset GlobalProtect gateways if service is unstable

### Cross-Domain Connectivity Troubleshooting ###
# Multi-Cloud Routing #
1. Azure to GCP Connectivity
   - Verify SD-WAN overlay between clouds
   - Check BGP route exchange
   - Validate firewall rules in both directions
   - Ensure proper NAT configuration if used
2. On-Premises to Cloud Connectivity
   - Verify SD-WAN branch to cloud paths
   - Check route propagation across domains
   - Validate firewall rules for on-prem subnets
   - Ensure proper DNS resolution across environments

# Common Remediation Actions #
1. Update routing policies in SD-WAN orchestrator
2. Modify firewall rules in both cloud environments
3. Reconfigure BGP attributes for proper path selection
4. Reset problematic network paths while maintaining others