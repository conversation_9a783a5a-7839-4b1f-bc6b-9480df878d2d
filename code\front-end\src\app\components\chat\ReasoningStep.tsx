import Icon from "../../icons/Icon";
import { IntermediateStep } from "@/actions/types";

interface ReasoningStepProps {
  step: IntermediateStep;
  index: number;
  isLast: boolean;
  isLastCompleted: boolean;
}

export default function ReasoningStep({ 
  step, 
  index, 
  isLast, 
  isLastCompleted 
}: ReasoningStepProps) {
  return (
    <div className="relative flex">
      <div className="flex flex-col items-center mr-3 relative">
        <div className="z-10">
        
          {/*step.status === "loading" ? (
            <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
          ) : (*/
            <div
              className={`w-4 h-4 border-2 rounded-full border-white flex items-center justify-center ${
                isLastCompleted ? "bg-green-500" : "bg-blue-600"
              }`}
            >
              <Icon name="check" className="w-2.5 h-2.5 text-white" />
            </div>
          }
        </div>
        {!isLast && (
          <div className="flex-1 w-0.5 bg-white" style={{ minHeight: 24 }} />
        )}
      </div>
      <div className="flex-1">
        <div className="text-sm text-white opacity-70 break-words whitespace-pre-line">{step.title}</div>
        <div className="text-sm mb-2 text-white opacity-90 break-words whitespace-pre-line">{step.content}</div>
      </div>
    </div>
  );
}
