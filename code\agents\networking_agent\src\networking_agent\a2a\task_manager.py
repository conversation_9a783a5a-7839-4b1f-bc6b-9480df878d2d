from typing import AsyncIterable
from networking_agent.a2a.common.server.task_manager import InMemoryTaskManager
from networking_agent.a2a.common.types import (
  Artifact,
  JSONRPCResponse,
  Message,
  SendTaskRequest,
  SendTaskResponse,
  SendTaskStreamingRequest,
  SendTaskStreamingResponse,
  Task,
  TaskState,
  TaskStatus,
  TextPart,
)
from networking_agent.graph.builder import query_graph, query_ticket
from networking_agent.a2a.api_types import convert_history, QueryResponse
import sys
import asyncio
import json
import logging


# Required for postgres checkpoint saver
if sys.platform.startswith("win"):
  asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())

class AgentTaskManager(InMemoryTaskManager):
  def __init__(self):
    super().__init__()

  async def on_send_task(self, request: SendTaskRequest) -> SendTaskResponse:
    logging.info("Recieved a query, trying to parse parts")
    # Upsert a task stored by InMemoryTaskManager
    await self.upsert_task(request.params)

    task_id = request.params.id
    ticket = None
    for part in request.params.message.parts:
      try:
        if isinstance(part, TextPart):
          received_text = part.text
          logging.info(f"Got the text part: {received_text}")
          parsed = json.loads(received_text)
          if "ticket" in parsed.keys():
            logging.info("Received ticket")
            ticket = parsed["ticket"]
          query = parsed.get('query')
          logging.info(f"Got the query: {query}")
          thread_id = parsed.get('thread_id')
          logging.info(f"Got the thread_id: {thread_id}")
      except Exception as e:
        logging.error(f"Error while parsing request {e}")
    
    try:
      response=None
      if ticket: 
        logging.info(f"Querying graph with ticket: {ticket} and threadID: {thread_id}")
        response = await query_ticket(ticket, thread_id=thread_id)
      else:
        logging.info(f"Querying graph with query: {query} and threadID: {thread_id}")
        response = await query_graph(received_text, thread_id=thread_id)
      messages=response["messages"]
      response_messages = convert_history(messages=messages)
      query_response = QueryResponse(response=response_messages[-1])
      task = await self._update_task(
        task_id=task_id,
        task_state=TaskState.COMPLETED,
        response_text=json.dumps(query_response.model_dump())
      )
      return SendTaskResponse(id=request.id, result=task)
    except Exception as e:
      logging.error(f"Error while querying graph: {e}")
  async def on_send_task_subscribe(
    self,
    request: SendTaskStreamingRequest
  ) -> AsyncIterable[SendTaskStreamingResponse] | JSONRPCResponse:
    pass

  async def _update_task(
    self,
    task_id: str,
    task_state: TaskState,
    response_text: str,
  ) -> Task:
    task = self.tasks[task_id]
    agent_response_parts = [
      {
        "type": "text",
        "text": response_text,
      }
    ]
    task.status = TaskStatus(
      state=task_state,
      message=Message(
        role="agent",
        parts=agent_response_parts,
      )
    )
    task.artifacts = [
      Artifact(
        parts=agent_response_parts,
      )
    ]
    return task
