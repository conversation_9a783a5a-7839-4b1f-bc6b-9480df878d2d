import path from 'path';
import { fileURLToPath } from 'url';
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,
  // Disable the development indicator (Next.js icon)
  devIndicators: false,
  eslint: {
    ignoreDuringBuilds: true,
  },/* {
    buildActivity: false,
    //buildActivityPosition: 'bottom-right',
  },*/// next.config.js,
  output: "standalone",
  webpack: (config: any,  { isServer }: { isServer: boolean }) => {
    // Add aliases for your custom paths defined in tsconfig.json
    // Use path.resolve for absolute paths relative to the project root
    config.resolve.alias['@/a2a'] = path.resolve(__dirname, 'src', 'a2a');
    config.resolve.alias['@/actions'] = path.resolve(__dirname, 'src', 'actions');
    config.resolve.alias['@/app'] = path.resolve(__dirname, 'src', 'app');

    // Return the modified config
    return config;
  },
}

module.exports = nextConfig
