# using existing secret for credentials
config:
  #existingSecret: networking-agent-secret
  client-id: ODhkOTQyNzktNWIzNS00MDg1LTkyZjktOGI4MWNmNjc5NWFm
  client-secret: ********************************************************
  cookie-secret: ********************************************

# Configure for Azure AD (Entra ID)
extraArgs:
  provider: azure
  azure-tenant: "a47bd588-f2c9-4146-b0b2-d39f56758e0e"
  oidc-issuer-url: "https://login.microsoftonline.com/a47bd588-f2c9-4146-b0b2-d39f56758e0e/v2.0"
  email-domain: "unisuper.com"
  upstream: "file:///dev/null" # Dummy upstream (recommended for ingress auth)
  http-address: "0.0.0.0:4180"
  redirect-url: "https://merlin.devops.lan/oauth2/callback"
  cookie-secure: "true"
  cookie-httponly: "true"
  cookie-samesite: "lax"
  set-xauthrequest: "true"
  pass-access-token: "true"
  pass-authorization-header: "true"
  pass-user-headers: "true"
  set-authorization-header: "true"

# Service configuration
service:
  type: ClusterIP
  portNumber: 4180

# Ingress for oauth2-proxy itself
ingress:
  enabled: true
  annotations:
    spec.ingressClassName: nginx
    nginx.ingress.kubernetes.io/ssl-redirect: "false"
  hosts:
    - merlin.devops.lan
  path: /oauth2
  # tls:
  #   - secretName: your-tls-secret
  #     hosts:
  #       - your-domain.com