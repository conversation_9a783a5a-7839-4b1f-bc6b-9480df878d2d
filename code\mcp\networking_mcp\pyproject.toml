[project]
name = "networking_mcp"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
authors = [
    { name = "<PERSON>", email = "149848050+clau<PERSON><PERSON><PERSON><PERSON><PERSON>@users.noreply.github.com" }
]
requires-python = ">=3.11"
dependencies = [
    "azure-devops>=7.1.0b4",
    "azure-search-documents>=11.5.2",
    "gitpython>=3.1.44",
    "mcp[cli]>=1.7.1",
    "openai>=1.78.0",
    "pip-audit>=2.9.0",
    "python-dotenv>=1.1.0",
]

[project.scripts]
mcp-server = "mcp_server:main"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"
