from networking_agent.a2a.common.server import A2AServer
from starlette.requests import Request
from starlette.responses import JSONResponse, Response
from langgraph.checkpoint.postgres.aio import AsyncPostgresSaver
import os
from networking_agent.a2a.api_types import ThreadSummary, ListThreadsResponse, ThreadHistoryResponse, convert_history
import uuid
import logging

class CustomA2AServer(A2AServer):
	def __init__(self, *args, **kwargs):
		super().__init__(*args, **kwargs)
		self.app.add_route("/threads/list", self.list_threads, methods=["GET"])
		self.app.add_route("/threads/list/ticket", self.list_ticket_threads, methods=["GET"])
		self.app.add_route("/threads/list/chat", self.list_chat_threads, methods=["GET"])
		self.app.add_route("/threads/history", self.thread_history, methods=["POST"])
		self.app.add_route("/threads/new", self.new_thread, methods=["POST"])
		
		self.app.add_route("/health", self.health_check, methods=["GET"])
		self.app.add_route("/ready", self.readiness_check, methods=["GET"])

	async def health_check(self, _request: Request) -> Response:
		logging.info("Health check requested.")
		return Response(status_code=200, content="OK")
	
	async def readiness_check(self, _request: Request) -> Response:
		logging.info("Readiness check requested.")
		return Response(status_code=200, content="OK")

	async def list_threads(self, _request: Request) -> JSONResponse:
		try:
			async with AsyncPostgresSaver.from_conn_string(os.environ["POSTGRESQL_URL"]) as checkpointer: 
				threads = checkpointer.alist(config=None)
				thread_ids = []
				list_threads = []
				async for thread in threads:
					thread_id = thread[0]["configurable"]["thread_id"]
					if not thread_id in thread_ids:
						title = thread[1]["channel_values"]["messages"][0].content
						list_threads.append(ThreadSummary(thread_id=thread_id, title=title))
						thread_ids.append(thread_id)
								
				response = ListThreadsResponse(threads=list_threads).model_dump()
				return JSONResponse(response, status_code=200)
		except Exception as e:
			return JSONResponse({"error": f"{e}"}, status_code=500)
	
	async def list_ticket_threads(self, _request: Request) -> JSONResponse:
		try:
			async with AsyncPostgresSaver.from_conn_string(os.environ["POSTGRESQL_URL"]) as checkpointer: 
				threads = checkpointer.alist(config=None)
				thread_ids = []
				list_threads = []
				async for thread in threads:
					thread_id = thread[0]["configurable"]["thread_id"] 
					state_keys = thread[1]["channel_values"].keys()
					if not thread_id in thread_ids and "ticket" in state_keys and "messages" in state_keys:
						title = thread[1]["channel_values"]["ticket"]["title"]
						list_threads.append(ThreadSummary(thread_id=thread_id, title=title))
						thread_ids.append(thread_id)
								
				response = ListThreadsResponse(threads=list_threads).model_dump()
				return JSONResponse(response, status_code=200)
		except Exception as e:
			logging.error(f"Error while fetching list of chat threads: {e}")
			return JSONResponse({"error": f"{e}"}, status_code=500)
		
	async def list_chat_threads(self, _request: Request) -> JSONResponse:
		try:
			async with AsyncPostgresSaver.from_conn_string(os.environ["POSTGRESQL_URL"]) as checkpointer: 
				threads = checkpointer.alist(config=None)
				thread_ids = []
				list_threads = []
				async for thread in threads:
					thread_id = thread[0]["configurable"]["thread_id"] 
					state_keys = thread[1]["channel_values"].keys()
					if not thread_id in thread_ids and "ticket" not in state_keys and "messages" in state_keys:
						title = thread[1]["channel_values"]["messages"][0].content
						list_threads.append(ThreadSummary(thread_id=thread_id, title=title))
						thread_ids.append(thread_id)
								
				response = ListThreadsResponse(threads=list_threads).model_dump()
				return JSONResponse(response, status_code=200)
		except Exception as e:
			logging.error(f"Error while fetching list of chat threads: {e}")
			return JSONResponse({"error": f"{e}"}, status_code=500)

	async def thread_history(self, request: Request) -> JSONResponse:
		try:
			body = await request.json()
			thread_id = body["thread_id"]
			async with AsyncPostgresSaver.from_conn_string(os.environ["POSTGRESQL_URL"]) as checkpointer: 
				config = {"configurable": {"thread_id": thread_id}}
				messages = (await checkpointer.aget(config))["channel_values"]["messages"]
				response_messages = convert_history(messages=messages)
				response = ThreadHistoryResponse(thread_id=thread_id, messages=response_messages)
				return JSONResponse(content=response.model_dump(), status_code=200)
		except Exception as e:
			return JSONResponse(content={"error": f"{e}"}, status_code=500)

	async def new_thread(self, _request: Request):
		try:
			thread_id = str(uuid.uuid4())
			payload = {"thread_id": thread_id}
			return JSONResponse(content=payload, status_code=200)
		except Exception as e:
			return JSONResponse(content={"error": f"{e}"}, status_code=500)
