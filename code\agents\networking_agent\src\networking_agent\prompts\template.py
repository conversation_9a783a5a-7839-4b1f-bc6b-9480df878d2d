from networking_agent.graph.state import State
from pathlib import Path

def apply_prompt_template(prompt_name: str, state: State):
    with open(Path(__file__).parent / f"{prompt_name}.md", "r") as file:
        system_prompt = file.read()
    return [{"role": "system", "content": system_prompt}] + state["messages"]

if __name__ == "__main__":
    print(apply_prompt_template("system", State(messages=[{"role": "user", "content": "Hello"}])))
