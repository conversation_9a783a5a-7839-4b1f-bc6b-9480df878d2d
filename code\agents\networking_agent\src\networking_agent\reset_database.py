""" 
WARNING: This script deletes ALL past conversations from the database, they cannot be recovered.

Note: You do not need to initialise the database again after running this script.
"""

import os
from langgraph.checkpoint.postgres import PostgresSaver
from dotenv import load_dotenv

load_dotenv()

if __name__ == "__main__":
    print("<PERSON><PERSON><PERSON> started.")
    try:
        thread_ids = []
        with PostgresSaver.from_conn_string(os.environ["POSTGRESQL_URL"]) as checkpointer:
            existing_threads = checkpointer.list(config=None)
            for thread in existing_threads:
                thread_id = thread[0]["configurable"]["thread_id"]
                if thread_id not in thread_ids:
                    thread_ids.append(thread_id)
        print(f"Found {len(thread_ids)} threads to be deleted")

        with PostgresSaver.from_conn_string(os.environ["POSTGRESQL_URL"]) as checkpointer:
            for thread_id in thread_ids:
                checkpointer.delete_thread(thread_id=thread_id)
                print(f"Deleting thread with ID: {thread_id}")

    except Exception as e:
        print(f"An error occurred: {e}")
        