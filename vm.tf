
# Configure Availability Set
resource "azurerm_availability_set" "as" {
  name                         = module.naming.availability_set_name
  location                     = module.resource_group.location
  resource_group_name          = module.resource_group.name
  platform_fault_domain_count  = 2
  platform_update_domain_count = 2
  managed                      = true # managed disks

  tags = local.common_tags
}

# VM in private network to support operations
module "vm" {
  source  = "app.terraform.io/unisuper/winsvr-vm/azurerm"
  version = "0.3.4"

  environment             = "non-prod"
  location                = module.resource_group.location
  vm_rg_name              = module.resource_group.name
  vm_name                 = module.naming.virtual_machine_windows_name
  vm_size                 = "Standard_D4s_v5"
  enable_managed_identity = true

  vnet_name                          = data.azurerm_virtual_network.app_vnet.name
  vnet_rg_name                       = data.azurerm_virtual_network.app_vnet.resource_group_name
  subnet_name                        = data.azurerm_subnet.pe_subnet2.name
  create_public_ip                   = false
  private_ip_address_allocation_type = "Dynamic"
  availability_set_id                = azurerm_availability_set.as.id

  disks = {
    disk1 = {
      disk_name            = "datadisk"
      storage_account_type = "StandardSSD_LRS"
      disk_size_gb         = 100
      lun                  = 0
    }
  }

  tags       = local.common_tags
  depends_on = [module.resource_group]
}
