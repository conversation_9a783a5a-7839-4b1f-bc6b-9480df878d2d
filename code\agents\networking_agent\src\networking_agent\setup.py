import asyncio
import os
from langgraph.checkpoint.postgres.aio import AsyncPostgresSaver
from dotenv import load_dotenv
import sys

load_dotenv()
if sys.platform.startswith("win"):
  asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())

async def setup():
    async with AsyncPostgresSaver.from_conn_string(os.environ["POSTGRESQL_URL"]) as checkpointer:
        await checkpointer.setup()

if __name__ == "__main__":
    asyncio.run(setup())
    print("Your database is setup and ready for use!")
    