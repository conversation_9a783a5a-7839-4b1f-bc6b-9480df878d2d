import React, { useState } from "react";
import ReactMarkdown from "react-markdown";
import AttachmentList from "./conversation/AttachmentList";
import UserMessage from "./chat/UserMessage";
import AgentHeader from "./chat/AgentHeader";
import ReasoningToggle from "./chat/ReasoningToggle";
import ReasoningDetails from "./chat/ReasoningDetails";
import CodeBlock from "./chat/CodeBlock";
import { ChatMessageProps } from "@/app/types";
import CreateTicketForm from "./CreateTicketForm";


export default function ChatMessage({
  id,
  type,
  content,
  timestamp,
  intermediate_steps,
  onCloseReasoning,
  showAttachments = false,
  showCreateTicketMessage = false,
  references,
  isLoading,
}: ChatMessageProps & { showCreateTicketMessage?: boolean }) {
  const [isReasoningExpanded, setIsReasoningExpanded] = useState(false);

  const handleCloseReasoning = () => {
    setIsReasoningExpanded(false);
    onCloseReasoning?.();
  };

  const handleToggleReasoning = () => {
    setIsReasoningExpanded(!isReasoningExpanded);
  };

  // Render CreateTicketForm as a special message
  if (showCreateTicketMessage) {
    return (
      <div className="flex justify-start mb-4">
        <div className="w-full">
          <CreateTicketForm />
        </div>
      </div>
    );
  }

  if (type === "human") {
    return <UserMessage content={content} timestamp={timestamp} />;
  }
  
  const isThinking = isLoading || (!content && id !== "thinking");
  const isThinkingMessage = id === "thinking";

  // Skip rendering the temporary empty AI message when we also have a dedicated thinking message
  if (!content && isLoading && !isThinkingMessage) {
    return null; // Don't render the temporary empty message when we have a dedicated thinking message
  }

  return (
    <div className="flex justify-start mb-4">
      <div className="max-w-full">
        {/* Only render AgentHeader for assistant role */}
        {type === "ai" && (
          <AgentHeader isThinking={isThinking} />
        )}

        {intermediate_steps && intermediate_steps.length > 0 && (
          <div className="mb-3 px-12">
            <ReasoningToggle
              thinkingTime={0}
              isExpanded={isReasoningExpanded}
              onToggle={handleToggleReasoning}
            />
            {isReasoningExpanded && (
              <ReasoningDetails
                steps={intermediate_steps}
                onClose={handleCloseReasoning}
              />
            )}
          </div>
        )}

        {content && (
          <div className="bg-white px-12 py-3">
            <div className="text-sm leading-relaxed text-gray-800">
              <ReactMarkdown
                components={{
                  p: ({ children }) => {
                    // Simplified p override - just render children within a paragraph
                    return <p className="mb-2 last:mb-0 text-sm leading-relaxed text-gray-800">{children}</p>;
                  },
                  strong: ({ children }) => (
                    <strong className="font-semibold text-gray-900">
                      {children}
                    </strong>
                  ),
                  h1: ({ children }) => (
                    <h1 className="text-lg font-bold mb-2 text-gray-900">
                      {children}
                    </h1>
                  ),
                  h2: ({ children }) => (
                    <h2 className="text-base font-bold mb-2 text-gray-900">
                      {children}
                    </h2>
                  ),
                  h3: ({ children }) => (
                    <h3 className="text-sm font-bold mb-1 text-gray-900">
                      {children}
                    </h3>
                  ),
                  ul: ({ children }) => (
                    <ul className="list-disc list-inside mb-2 space-y-1">
                      {children}
                    </ul>
                  ),
                  ol: ({ children }) => (
                    <ol className="list-decimal list-inside mb-2 space-y-1">
                      {children}
                    </ol>
                  ),
                  li: ({ children }) => <li className="text-sm">{children}</li>,
                  pre: ({ children }) => {
                    // Handle pre tag by passing isBlock to the code component
                    if (React.isValidElement(children)) {
                      return React.cloneElement(children, { isBlock: true } as any);
                    }
                    return <>{children}</>;
                  },
                  code: (props: any) => {
                    const { className, children, isBlock, ...rest } = props;
                    // If it's a block code (wrapped in pre) or has a language class, use CodeBlock
                    if (isBlock || className?.includes('language-')) {
                      return (
                        <CodeBlock
                          className={className}
                          isBlock={true}
                          {...rest}
                        >
                          {String(children).replace(/\n$/, "")}
                        </CodeBlock>
                      );
                    }
                    // Otherwise, it's inline code
                    return (
                      <code className="bg-gray-100 px-1.5 py-0.5 rounded text-xs font-mono break-words whitespace-pre-wrap" {...rest}>
                        {children}
                      </code>
                    );
                  },
                }}
              >
                {content}
              </ReactMarkdown>
            </div>
          </div>
        )}
        {showAttachments && <AttachmentList references={references || []} />}
      </div>
    </div>
  );
}
