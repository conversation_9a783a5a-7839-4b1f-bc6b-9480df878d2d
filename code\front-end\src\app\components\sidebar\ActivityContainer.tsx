import React, { useState } from "react";
import Activity from "./Activity";

export default function ActivityContainer() {
  const [activitySections, setActivitySections] = useState([
    { title: "Awaiting Input", count: 2, isOpen: true },
    { title: "In Process", count: 0, isOpen: false },
    { title: "Assigned to", count: 0, isOpen: false },
    { title: "Archive", count: 128, isOpen: false },
  ]);

  const handleActivitySectionToggle = (title: string) => {
    setActivitySections(prev =>
      prev.map(section =>
        section.title === title ? { ...section, isOpen: !section.isOpen } : section
      )
    );
  };

  return (
    <Activity 
      sections={activitySections} 
      onSectionToggle={handleActivitySectionToggle} 
    />
  );
}
