# Deploy NGINX Ingress Controller with Helm

## 1. Add the ingress-nginx Helm repository
```sh helm repo add ingress-nginx https://kubernetes.github.io/ingress-nginx
helm repo update
```

## 2. Create the namespace (if not already created)
```sh
kubectl create namespace ingress-nginx
```

## 3. Install the ingress controller using your values file
```sh
helm install ingress-nginx ingress-nginx/ingress-nginx \
  --namespace ingress-nginx \
  --values nginx.yaml
```
- The release will be named `ingress-nginx` and deployed in the `ingress-nginx` namespace.