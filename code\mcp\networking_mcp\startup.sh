#!/bin/bash

#set -euo pipefail

startup() {
  echo "[INFO] Updating apt cache and installing Git..."
  apt-get update -y && apt-get install -y git

  echo "[INFO] Configuring Git..."
  git config --global user.name "Chee <PERSON>"
  git config --global user.email "<EMAIL>"
  git config --global --add safe.directory /home/<USER>/wwwroot/networking_mcp-0.1.0/repository/azure-usm-non-production-network
  git config --global --add safe.directory /home/<USER>/wwwroot/networking_mcp-0.1.0/repository/azure-connectivity-non-production
  git config --global --add safe.directory /home/<USER>/wwwroot/networking_mcp-0.1.0/repository/azure-next-gen-firewall-vwan
 
  APP_DIR="networking_mcp-0.1.0"
  SCRIPT_PATH="./src/networking_mcp/__init__.py"

  echo "[INFO] Switching to application directory: $APP_DIR"

  echo "[INFO] Installing uv..."
  pip install uv

  echo "[INFO] Launching application..."
  uv run mcp run "$SCRIPT_PATH" --transport sse
}

# Run the startup function and catch errors
if ! startup; then
  echo "[ERROR] Startup script failed" >&2
  exit 1
fi