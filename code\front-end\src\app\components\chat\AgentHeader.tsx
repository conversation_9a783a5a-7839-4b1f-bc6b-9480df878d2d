import React from "react";

interface AgentHeaderProps {
  isThinking: boolean | undefined;
}

export default function AgentHeader({ isThinking }: AgentHeaderProps) {
  return (
    <div className="px-4 mb-2">
      {/* Agent Name and Status */}
      <div className="flex items-center space-x-2">
        <div className="w-6 h-6 rounded-full bg-gradient-to-b from-orange-500 to-orange-100 flex items-center justify-center">
          <img
            src="/n.png"
            alt="N"
            width="12"
            height="12"
            className="object-contain"
          />
        </div>
        {isThinking ? (
          <div className="bg-white rounded px-2 py-1 flex items-center space-x-2">
            <span className="text-xs font-medium">
              Network Agent is thinking
            </span>
            <div className="flex space-x-1 items-end">
              <div
                className="w-2 h-2 rounded-full bg-gray-500 animate-bounce"
                style={{ animationDelay: "0ms" }}
              ></div>
              <div
                className="w-2 h-2 rounded-full bg-gray-500 animate-bounce"
                style={{ animationDelay: "150ms" }}
              ></div>
              <div
                className="w-2 h-2 rounded-full bg-gray-500 animate-bounce"
                style={{ animationDelay: "300ms" }}
              ></div>
            </div>
          </div>
        ) : (
          <span className="text-xs font-medium">Network Agent</span>
        )}
      </div>
    </div>
  );
}
