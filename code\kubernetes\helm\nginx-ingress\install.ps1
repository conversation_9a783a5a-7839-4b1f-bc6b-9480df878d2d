# nginx-ingress/install.ps1
param(
    [string]$Namespace = "ingress-nginx",
    [string]$ReleaseName = "nginx-ingress",
    [string]$ValuesFile = "values.yaml"
)

# Set error handling
$ErrorActionPreference = "Stop"

Write-Host "Installing/Upgrading $ReleaseName in namespace $Namespace..." -ForegroundColor Green

try {
    # Create namespace if it doesn't exist
    Write-Host "Creating namespace $Namespace if it doesn't exist..." -ForegroundColor Yellow
    kubectl create namespace $Namespace --dry-run=client -o yaml | kubectl apply -f -
    
    if ($LASTEXITCODE -ne 0) {
        throw "Failed to create namespace"
    }

    # Install or upgrade the Helm release
    Write-Host "Installing/Upgrading Helm release..." -ForegroundColor Yellow
    helm upgrade --install $ReleaseName `
        ingress-nginx/ingress-nginx `
        --repo https://kubernetes.github.io/ingress-nginx `
        --namespace $Namespace `
        --values $ValuesFile `
        --wait `
        --timeout 10m
    
    if ($LASTEXITCODE -ne 0) {
        throw "Helm installation failed"
    }

    Write-Host "Successfully installed/upgraded $ReleaseName!" -ForegroundColor Green
    
    # Display release status
    helm status $ReleaseName -n $Namespace
}
catch {
    Write-Error "Error during installation: $_"
    exit 1
}