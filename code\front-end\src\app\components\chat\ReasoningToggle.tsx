import React from "react";
import Icon from "../../icons/Icon";

interface ReasoningToggleProps {
  thinkingTime?: number;
  isExpanded: boolean;
  onToggle: () => void;
}

export default function ReasoningToggle({ 
  thinkingTime, 
  isExpanded, 
  onToggle 
}: ReasoningToggleProps) {
  return (
    <div className="mb-3">
      <div className="flex items-center space-x-2">
        <span className="text-[10px] text-gray-500">
          {thinkingTime
            ? `Thought for ${thinkingTime}s`
            : "Thought process"}
        </span>
        <button
          onClick={onToggle}
          className={`w-3 h-3 rounded-full flex items-center justify-center transition-colors ${
            isExpanded
              ? "bg-blue-100 hover:bg-blue-200"
              : "bg-gray-200 hover:bg-gray-300"
          }`}
        >
          <Icon
            name="expand"
            className={`w-2.5 h-2.5 transition-transform ${
              isExpanded
                ? "rotate-90 text-blue-600"
                : "text-gray-600"
            }`}
            isExpanded={isExpanded}
          />
        </button>
      </div>
    </div>
  );
}
