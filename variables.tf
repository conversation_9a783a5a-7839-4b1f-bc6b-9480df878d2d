variable "environment" {
  description = "(Required) environment code"
  type        = string
  default     = "non-production"
  validation {
    condition = (
      can(var.environment) &&
      contains(["production", "non-production"], lower(var.environment)) #changed non-prod
    )
    error_message = "A Environment tag is required and only takes \"prod\" and \"non-prod\""
  }
}
variable "unique_id" {
  type    = string
  default = "devopsai"
}
variable "location" {
  type    = string
  default = "australiasoutheast" # australia east doesn't exist for non-prod
}

variable "subscription_id" {
  description = "The subscription where the vm is located"
  type        = string
  default     = "0290fa34-a3a4-484a-8ca5-b898c2e52fa8"
}

# dns
variable "subscription_private_dns_zone_id" {
  type        = string
  description = "The subscription ID to use for the AzureRM Provider."
  default     = "a0368199-95f7-4289-ae92-401ddc1731d3" # USM Non-Prod Subscription ID
}

# storage account
variable "account_kind" {
  description = "The kind of Storage Account to create."
  type        = string
  default     = "StorageV2"
}

variable "account_tier" {
  description = "Defines the Tier of the Storage Account."
  type        = string
  default     = "Standard"
}

variable "account_replication_type" {
  description = "Defines the type of replication to use for this Storage Account."
  type        = string
  default     = "LRS"
}

variable "public_network_access_enabled" {
  description = "Indicates whether the storage account is accessible from the public network."
  type        = bool
  default     = true
}

variable "enable_https_traffic_only" {
  description = "Enforces that all data to the Storage Account is transferred via HTTPS."
  type        = bool
  default     = true
}

variable "min_tls_version" {
  description = "The minimum TLS version to be permitted on requests to storage."
  type        = string
  default     = "TLS1_2"
}

variable "container_access_type" {
  description = "Defines the level of access allowed to the data in the container. Can be private, blob, or container."
  type        = string
  default     = "private"
}

variable "is_hns_enabled" {
  description = "Is Hierarchical Namespace enabled?"
  type        = bool
  default     = false
}

variable "allow_nested_items_to_be_public" {
  description = "Allow or disallow public access to nested items within the storage account."
  type        = bool
  default     = false
}

variable "workspace_managed_network" {
  type = object({
    isolation_mode = string
    spark_ready    = optional(bool, false)
  })
  default = {
    isolation_mode = "AllowOnlyApprovedOutbound"
    spark_ready    = false
  }
  description = <<DESCRIPTION
Specifies properties of the workspace's managed virtual network.

Possible values for `isolation_mode` are:
- 'Disabled': Inbound and outbound traffic is unrestricted _or_ BYO VNet to protect resources.
- 'AllowInternetOutbound': Allow all internet outbound traffic.
- 'AllowOnlyApprovedOutbound': Outbound traffic is allowed by specifying service tags.
While is possible to update the workspace to enable network isolation ('AllowInternetOutbound' or 'AllowOnlyApprovedOutbound'), it is not possible to disable it on a workspace with it enabled.

`spark_ready` determines whether spark jobs will be run on the network. This value can be updated in the future.
DESCRIPTION
}
variable "outbound_rules" {
  type = map(object({
    resource_id         = string
    sub_resource_target = string
  }))
  default     = {}
  description = <<DESCRIPTION
  A map of private endpoints outbound rules for the managed network.

  - `resource_id` - The resource id for the corresponding private endpoint.
  - `sub_resource_target` - The sub_resource_target is target for the private endpoint. e.g. account for Openai, searchService for Azure Ai Search

  DESCRIPTION
}
# variable "storage_access_type" {
#   type        = string
#   default     = "identity"
#   description = "The auth mode used for accessing the system datastores of the workspace - accessKey or identity."
# }

variable "azure_ai_sku" {
  description = "The pricing tier of the search service you want to create (for example, basic or standard)."
  default     = "standard"
  type        = string
  validation {
    condition     = contains(["free", "basic", "standard", "standard2", "standard3", "storage_optimized_l1", "storage_optimized_l2"], var.azure_ai_sku)
    error_message = "The sku must be one of the following values: free, basic, standard, standard2, standard3, storage_optimized_l1, storage_optimized_l2."
  }
}

variable "azure_ai_replica_count" {
  type        = number
  description = "Replicas distribute search workloads across the service. You need at least two replicas to support high availability of query workloads (not applicable to the free tier)."
  default     = 1
  validation {
    condition     = var.azure_ai_replica_count >= 1 && var.azure_ai_replica_count <= 12
    error_message = "The replica_count must be between 1 and 12."
  }
}

variable "azure_ai_partition_count" {
  type        = number
  description = "Partitions allow for scaling of document count as well as faster indexing by sharding your index over multiple search units."
  default     = 1
  validation {
    condition     = contains([1, 2, 3, 4, 6, 12], var.azure_ai_partition_count)
    error_message = "The partition_count must be one of the following values: 1, 2, 3, 4, 6, 12."
  }
}
variable "azure_ai_allowed_ips" {
  type        = list(string)
  default     = null
  description = "One or more IP Addresses, or CIDR Blocks which should be able to access the AI Search service"
}
variable "is_private" {
  type        = bool
  default     = false
  description = "Specifies if every provisioned resource should be private and inaccessible from the Internet."
}
variable "COSMOS_DB_PASSWORD" {
  description = "The password for the Cosmos DB account."
  type        = string
  # ephemeral   = true
}
variable "COSMOS_DB_ACCOUNT_NAME" {
  description = "value for the Cosmos DB account name."
  default     = "usmadmincosmosdb"
  type        = string
}