# MCP - Networking Agent
This is the MCP server for the Networking Agent.

The python version and dependencies for this service are listed in `.python-version` and `pyproject.toml` respectively.

### Git Credentials Setup

Git operations are necessary for the MCP server to clone the relevant repositories, make changes through git
diff, and raise pull requests.
​
In this PoV, the git credentials to perform these operations must be set in environment during startup.​
- First, ensure you have git installed, otherwise install git into your container.​
- Mark all the repositories in the MCP server as a safe directory​
- Add your git credentials with the required permissions to clone, create a new branch, and raise a pull
request.

### Running
First, please read the 'Running Merlin' section of the main `README` in the root of this repository, and ensure you've completed the necessary setup.

This project uses uv as the package and project manager for each MCP server.
```bash
cd ./mcp/networking_mcp
uv venv
uv sync
uv run mcp run ./src/networking_mcp/__init__.py --transport sse
```

The host and port can be specified as environment variables `MCP_HOST` and `MCP_PORT` respectively.
