module "naming" {
  source = "app.terraform.io/unisuper/naming/azurerm"

  unique_id   = var.unique_id
  environment = var.environment
  location    = var.location
}

module "resource_group" {
  source = "app.terraform.io/unisuper/resource-group/azurerm"

  name     = module.naming.resource_group_name
  location = module.naming.location

  tags = local.common_tags
}
resource "azurerm_role_assignment" "rbac" {
  for_each             = { for idx, val in local.role_assignments : idx => val }
  scope                = each.value.scope
  role_definition_name = each.value.role_definition_name
  principal_id         = each.value.principal_id
}

# resource "azurerm_mongo_cluster" "cosmosdb" {
#   name                   = module.naming.cosmos_db_database_name
#   resource_group_name    = module.resource_group.name
#   location               = "australiaeast"
#   administrator_username = var.COSMOS_DB_ACCOUNT_NAME
#   administrator_password = var.COSMOS_DB_PASSWORD
#   shard_count            = "1"
#   compute_tier           = "Free"
#   high_availability_mode = "Disabled"
#   storage_size_in_gb     = "32"
#   version                = "7.0"

#   tags = local.common_tags
# }


resource "azurerm_cosmosdb_postgresql_cluster" "cosmosdb" {
  name                            = module.naming.cosmos_db_database_name
  resource_group_name             = module.resource_group.name
  location                        = "australiaeast"
  administrator_login_password    = var.COSMOS_DB_PASSWORD
  coordinator_storage_quota_in_mb = 131072
  coordinator_vcore_count         = 2
  node_count                      = 0

  tags = local.common_tags
}