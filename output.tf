# output "vm_name" {
#   description = "Name of the deployed VM"
#   value       = module.vm.vm_name
# }

# output "resource_group_name" {
#   description = "Name of the resource group containing the VM"
#   value       = module.resource_group.name
# }

# output "availability_set_name" {
#   description = "Name of the availability set"
#   value       = azurerm_availability_set.as.name
# }
# output "storage_account_name" {
#   description = "Name of the storage_account"
#   value       = azurerm_storage_account.default.name
# }
# output "key_vault_name" {
#   description = "Name of the azurerm_key_vault"
#   value       = azurerm_key_vault.default.name
# }
# output "ai_search_name" {
#   description = "Name of the ai_service"
#   value       = azapi_resource.AIServicesResource.name
# }
# output "ai_hub_name" {
#   description = "Name of the ai_hub"
#   value       = azapi_resource.hub.name
# }
# output "ai_service_name" {
#   description = "Name of the ai_servicesConnection"
#   value       = azapi_resource.AIServicesConnection.name
# }
# output "ai_search_service_name" {
#   description = "Name of the ai_servicesConnection"
#   value       = azurerm_search_service.ai_search.name
# }
# output "ai_search_private_endpoint_name" {
#   description = "Name of the aisearch_private_endpoint"
#   value       = module.aisearch_private_endpoint.private_endpoint_id
# }

# output "aihub_private_endpoint_name" {
#   description = "Name of the aihub_private_endpoint"
#   value       = module.aihub_private_endpoint.private_endpoint_id
# }
# output "sa_blob_private_endpoint_name" {
#   description = "Name of the sa_blob_private_endpoint"
#   value       = module.sa_blob_private_endpoint.private_endpoint_id
# }
# output "sa_file_private_endpoint_name" {
#   description = "Name of the sa_file_private_endpoint"
#   value       = module.sa_file_private_endpoint.private_endpoint_id
# }
# output "kv_private_endpoint_name" {
#   description = "Name of the kv_private_endpoint"
#   value       = module.kv_private_endpoint.private_endpoint_id
# }
# output "aiservice_private_endpoint_name" {
#   description = "Name of the aiservice_private_endpoint"
#   value       = module.aiservice_private_endpoint.private_endpoint_id
# }


