resource "azurerm_cognitive_account" "openai" {
  name                          = "demoopenaiagent"
  resource_group_name           = module.resource_group.name
  location                      = "australiaeast"
  kind                          = "OpenAI"
  sku_name                      = "S0"
  tags                          = local.common_tags
  public_network_access_enabled = var.is_private
  custom_subdomain_name         = "demoopenaiagent"
  identity {
    type = "SystemAssigned"
  }
  network_acls {
    bypass         = "AzureServices"
    default_action = "Allow"
  }
}

resource "azurerm_cognitive_deployment" "openai" {
  name                   = "agenticopenai"
  cognitive_account_id   = azurerm_cognitive_account.openai.id
  rai_policy_name        = "Microsoft.DefaultV2"
  version_upgrade_option = "NoAutoUpgrade"

  model {
    format  = "OpenAI"
    name    = "text-embedding-3-small"
    version = "1"
  }

  sku {
    capacity = 202
    name     = "Standard" #Need to change to GlobalStandard
  }
}
# resource "azurerm_cognitive_deployment" "openai-model2" {
#   name                   = "gpt-4.1"
#   cognitive_account_id   = azurerm_cognitive_account.openai.id
#   rai_policy_name        = "Microsoft.DefaultV2"
#   version_upgrade_option = "NoAutoUpgrade"

#   model {
#     format  = "OpenAI"
#     name    = "gpt-4.1"
#     version = "1"
#   }

#   sku {
#     capacity = 700
#     name     = "GlobalStandard"
#   }
# }
