trigger: none

variables:
  vmImageName: 'aks-ado-agent-npd'
  environmentName: 'agenticai'
  npm_config_cache: $(Pipeline.Workspace)/.npm
  
stages:
- stage: Build
  displayName: Build stage
  jobs:
  - job: Build
    displayName: Build
    pool:
      #vmImage: $(vmImageName)
      name: 'aks-ado-agent-npd'

    steps:
    - task: NodeTool@0
      inputs:
        versionSpec: '22.x'
      displayName: 'Install Node.js'

    - script: |
        set -e  # Stops execution on error
        cd code/front-end 
        npm install || { echo "Error: Failed to do npm install"; exit 1; }
        npm run build || { echo "Error: Failed to do npm build"; exit 1; }
      displayName: 'Build'

    - task: ArchiveFiles@2
      inputs:
        rootFolderOrFile: '$(System.DefaultWorkingDirectory)'
        includeRootFolder: false
        archiveType: 'zip'
        archiveFile: '$(Build.ArtifactStagingDirectory)/$(Build.BuildId).zip'
        replaceExistingArchive: true

    - task: PublishBuildArtifacts@1
      inputs:
        PathtoPublish: '$(Build.ArtifactStagingDirectory)/$(Build.BuildId).zip'
        ArtifactName: 'drop'

    - task: AzureWebApp@1
      displayName: 'Deploy Webapp'
      inputs:
        appName: 'wa-devopsai-npd-mel-nwagt'
        azureSubscription: 'ado-management-federate-sc-management-non-production-Agentic-AI'
        package: '$(Build.ArtifactStagingDirectory)/$(Build.BuildId).zip'
        appType: webAppLinux
        startUpCommand: 'cd code/front-end && npm run start'
        #startUpCommand: 'cd code/front-end && npm install && npm run build && npm run start'