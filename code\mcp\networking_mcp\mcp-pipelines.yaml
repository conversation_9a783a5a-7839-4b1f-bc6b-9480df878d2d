trigger: none

variables:
  vmImageName: 'ubuntu-latest'
  environmentName: 'agenticai'
  
stages:
- stage: Build
  displayName: Build stage
  jobs:
  - job: Build
    displayName: Build
    pool:
      #vmImage: $(vmImageName)
      name: 'aks-ado-agent-npd'
      #vmImage: $(vmImageName)
      name: 'aks-ado-agent-npd'
    steps:
  #  - task: UsePythonVersion@0
  #    inputs:
  #      versionSpec: '3.11'
  #      architecture: 'x64'
  #  - task: UsePythonVersion@0
  #    inputs:
  #      versionSpec: '3.11'
  #      architecture: 'x64'
    - script: |
        python -m pip install --upgrade pip
        pip install uv
      displayName: 'Install uv dependencies'  
    - script: |
        set -e  # Stops execution on error
        cd code/mcp/networking_mcp
        uv venv || { echo "Error: Failed to create virtual environment"; exit 1; }
        uv lock --upgrade || { echo "Error: Failed to upgrade lock file"; exit 1; }
        uv sync || { echo "Error: Failed to sync dependencies"; exit 1; }
        uv build || { echo "Error: Build failed"; exit 1; }
        tar -xvzf dist/networking_mcp-0.1.0.tar.gz
        zip -r webapp.zip networking_mcp-0.1.0/*
        tar -xvzf dist/networking_mcp-0.1.0.tar.gz
        zip -r webapp.zip networking_mcp-0.1.0/*
        #uv run mcp run ./src/networking_mcp/__init__.py --transport sse
      displayName: 'Build the project'

    - task: PublishBuildArtifacts@1
      inputs:
        PathtoPublish: '$(Pipeline.Workspace)/s/code/mcp/networking_mcp/webapp.zip'
        PathtoPublish: '$(Pipeline.Workspace)/s/code/mcp/networking_mcp/webapp.zip'
        ArtifactName: 'drop'
    - task: AzureWebApp@1
      displayName: 'Deploy Webapp'
      inputs:
        appName: 'wa-devopsai-npd-mel-mcp'
        azureSubscription: 'ado-management-federate-sc-management-non-production-Agentic-AI'
        package: '$(Pipeline.Workspace)/s/code/mcp/networking_mcp/webapp.zip'
        appType: webAppLinux
        #startUpCommand: 'apt-get update && apt-get install -y git && cd networking_mcp-0.1.0 && pip install uv && uv run mcp run ./src/networking_mcp/__init__.py --transport sse'
        startUpCommand: 'cd networking_mcp-0.1.0 && sh startup.sh' 