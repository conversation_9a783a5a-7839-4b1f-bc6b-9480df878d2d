parameters:
  repoName: ''

steps:
  - task: AzureCLI@2
    displayName: "Get Metadata for Repo ${{ parameters.repoName }}"
    inputs:
      azureSubscription: $(serviceConnection)
      scriptType: bash
      scriptLocation: inlineScript
      inlineScript: |
        metadataBlob="${{ parameters.repoName }}-metadata.json"
        metadata=$(az storage blob metadata show \
          --account-name $(storageAccount) \
          --container-name $(containerName) \
          --name "$metadataBlob" \
          --auth-mode login || echo "{}")
        lastCommit=$(echo $metadata | jq -r '.lastcommit // empty')
        echo "##vso[task.setvariable variable=LAST_COMMIT]$lastCommit"

  - script: |
      if [ -z "$LAST_COMMIT" ]; then
        BASE_COMMIT=$(git rev-list HEAD | tail -n 1)
      else
        BASE_COMMIT=$LAST_COMMIT
      fi
      git diff --name-only $BASE_COMMIT HEAD > changed_files.txt
    displayName: "Detect Changes - ${{ parameters.repoName }}"

#  - task: AzureCLI@2
#    displayName: "Upload Changes for ${{ parameters.repoName }}"
#    inputs:
#      azureSubscription: $(serviceConnection)
#      scriptType: bash
#      scriptLocation: inlineScript
#      inlineScript: |
#        while IFS= read -r file; do
#          if [ -f "$file" ]; then
#            az storage blob upload \
#              --account-name $(storageAccount) \
#              --container-name $(containerName) \
#              --name "${{ parameters.repoName }}/$file" \
#              --file "$file" \
#              --overwrite \
#              --auth-mode login
#          fi
#        done < changed_files.txt

#  - task: AzureCLI@2
#    displayName: "Update Metadata - ${{ parameters.repoName }}"
#    inputs:
#      azureSubscription: $(serviceConnection)
#      scriptType: bash
#      scriptLocation: inlineScript
#      inlineScript: |
#        currentCommit=$(git rev-parse HEAD)
#        az storage blob metadata update \
#          --account-name $(storageAccount) \
#          --container-name $(containerName) \
#          --name "${{ parameters.repoName }}-metadata.json" \
#          --metadata lastcommit=$currentCommit \
#          --auth-mode login