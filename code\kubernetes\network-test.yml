apiVersion: v1
kind: Pod
metadata:
  name: network-test-pod
  namespace: networking # Deploying in the 'networking' namespace
spec:
  containers:
  - name: busybox-container
    image: busybox:latest # A lightweight image with basic network tools
    command: ["sh", "-c", "while true; do echo 'Pod is running for network testing...'; sleep 3600; done"] # Keeps the pod running
  restartPolicy: Never # Ensures the pod doesn't restart automatically if it exits
