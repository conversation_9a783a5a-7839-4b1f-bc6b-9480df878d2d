"use client";

import { useEffect, useState } from "react";

export default function IconBlocker() {
  const [mounted, setMounted] = useState(false);
  
  useEffect(() => {
    setMounted(true);
    
    // Try to remove the Next.js icon directly
    const removeIcon = () => {
      const icons = document.querySelectorAll('[id^="__next"], [class^="nextjs-"]');
      icons.forEach(icon => {
        if (icon.id !== "__next" && !icon.classList.contains("nextjs-toast-errors-parent")) {
          icon.remove();
        }
      });
    };
    
    removeIcon();
    const interval = setInterval(removeIcon, 500);
    
    return () => clearInterval(interval);
  }, []);
  
  if (!mounted) return null;
  
  return (
    <>
      {/* This is a transparent overlay that blocks only the bottom-right corner */}
      <style jsx global>{`
        /* Create a global style to hide any Next.js related elements */
        [id^="__next-"]:not(#__next),
        [class^="nextjs-"]:not(.nextjs-toast-errors-parent) {
          display: none !important;
          opacity: 0 !important;
          visibility: hidden !important;
          pointer-events: none !important;
          width: 0 !important;
          height: 0 !important;
        }
      `}</style>
      
      {/* Bottom-right corner blocker */}
      <div 
        style={{
          position: 'fixed',
          bottom: 0,
          right: 0,
          width: '100px',
          height: '100px',
          background: 'white',
          zIndex: 9999999,
          pointerEvents: 'all'
        }}
      />
      
      {/* Full-screen overlay that's transparent to clicks */}
      <div 
        style={{
          position: 'fixed',
          top: 0,
          left: 0,
          width: '100vw',
          height: '100vh',
          background: 'transparent',
          zIndex: 9999998,
          pointerEvents: 'none'
        }}
      />
    </>
  );
}
