## This script is used to maintain the ACR task for the Agentic AI frontend application.
# The init section is used to create the ACR task if it does not already exist.

# The run task is used to trigger the task to build the Docker image.

$ACR_NAME = 'acrsbxl5binteg'
# Token - Code Read permissions required
# Set the environment variable to prevent token being exposed in logs
$ENV:GIT_PAT = 'Your token here'

# URL for repo has this format: gitUrl[#Branchname[:subfolder]]
# https git url - https://<EMAIL>/UniSuper-Infrastructure/Agentic-AI/_git/agentic-ai-pov-capgemini
# branch name - feature/containers
# subfolder - code/mcp/networking_mcp

$organisation = 'UniSUper-Infrastructure'
$project = 'Agentic-AI'
$repo = 'agentic-ai-pov-capgemini'
$branch = 'feature/containers'
$folder = 'code/mcp/networking_mcp'

$name = 'agentic-ai-mcp'

$GIT_REPO = "https://dev.azure.com/$($organisation)/$($project)/_git/$repo#$($branch):$($folder)"

# Use the following command to create the ACR task
az acr task create --registry $ACR_NAME --name task-$name --context $GIT_REPO --file acrtask-$($name).yaml --git-access-token $env:GIT_PAT --commit-trigger-enabled false

# Use the following command to disable the commit trigger
az acr task update --registry $ACR_NAME --name task-$name --commit-trigger-enabled false

# Use the following command to manually run the task
az acr task run --registry $ACR_NAME --name task-$name
