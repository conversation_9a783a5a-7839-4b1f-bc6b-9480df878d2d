# --- Stage 1: Builder ---
# This stage installs dependencies and builds our application.
FROM python:3.11-slim AS builder

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1
ENV VIRTUAL_ENV=/opt/venv

# Create a virtual environment
RUN python3 -m venv $VIRTUAL_ENV
ENV PATH="$VIRTUAL_ENV/bin:$PATH"

# Install system dependencies needed for building
RUN apt-get update && \
    apt-get install -y --no-install-recommends git ca-certificates && \
    rm -rf /var/lib/apt/lists/*

# Install uv, our Python package manager
RUN pip install --no-cache-dir uv

WORKDIR /app

# Copy only the files needed to install dependencies
COPY . .

# Install all Python dependencies from pyproject.toml into the virtual environment
# This includes your app's dependencies like fastmcp, azure-core, etc.
RUN uv pip install --system --no-cache .

# --- START: Git ASKPASS setup (during build) ---
# Create the git-askpass.sh script during the build.
# It will echo the ADO_PAT environment variable, which is available at runtime.
RUN echo '#!/bin/sh' > /app/git-askpass.sh && \
    echo 'echo "$ADO_PAT"' >> /app/git-askpass.sh && \
    chmod +x /app/git-askpass.sh

# Set the GIT_ASKPASS environment variable to point to our script.
# This tells git to use this script to get credentials instead of prompting.
ENV GIT_ASKPASS=/app/git-askpass.sh
# --- END: Git ASKPASS setup ---

# --- Stage 2: Production ---
# This stage creates the final, lean production image.
FROM python:3.11-slim AS production

# Install system dependencies needed for building
RUN apt-get update && \
    apt-get install -y --no-install-recommends git ca-certificates && \
    pip install --no-cache-dir uv uvicorn fastmcp starlette azure-core azure-search-documents openai azure-devops gitpython python-dotenv mcp[cli] pip-audit&& \
    rm -rf /var/lib/apt/lists/*

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1
ENV VIRTUAL_ENV=/opt/venv
ENV PATH="$VIRTUAL_ENV/bin:$PATH"
# Add the 'src' directory to the Python path
ENV PYTHONPATH="${PYTHONPATH}:/app/src"

# Create a non-root user to run the application
RUN groupadd -r appgroup && useradd -r -g appgroup -u 1000 -d /app -s /bin/sh appuser

WORKDIR /app

# Copy the application source code from the builder stage into the current directory (/app)
COPY --from=builder /app .
# Create and assign permissions for directories the application needs to write to
# This includes the directory for cloned git repositories.
RUN mkdir -p /app/repository /app/.azure_devops_cache && \
    chown -R appuser:appgroup /app

# Switch to the non-root user
USER appuser

# Configure git for the appuser at runtime
RUN git config --global user.name "Chee How" && \
    git config --global user.email "<EMAIL>" && \
    git config --global --add safe.directory '*'

# Expose the port the app runs on
EXPOSE 8000

# This is the correct command to run your application using the official CLI
CMD ["uv", "run", "./src/networking_mcp/__init__.py"]
# CMD ["uvicorn", "networking_mcp.__init__:mcp", "--host", "0.0.0.0", "--port", "8000"]
# CMD ["fastmcp", "run", "networking_mcp", "--transport", "sse", "--host", "0.0.0.0", "--port", "8000"]