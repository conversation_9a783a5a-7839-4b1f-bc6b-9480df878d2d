// src/app/utils/config.ts
interface AppConfig {
  a2aUrl: string;
  nodeEnv: string;
  isDevelopment: boolean;
  isProduction: boolean;
}

function validateRequiredEnvVar(name: string, value: string | undefined): string {
  if (!value) {
    throw new Error(`Missing required environment variable: ${name}`);
  }
  return value;
}

function createConfig(): AppConfig {
  const nodeEnv = process.env.NODE_ENV || 'development';
  const isDevelopment = nodeEnv === 'development';
  const isProduction = nodeEnv === 'production';

  // In production, A2A_URL is required
  // In development, fallback to localhost
  let a2aUrl: string;
  
  if (isProduction) {
    a2aUrl = validateRequiredEnvVar('A2A_URL', process.env.A2A_URL);
  } else {
    a2aUrl = process.env.A2A_URL || 'http://localhost:10002';
  }

  return {
    a2aUrl,
    nodeEnv,
    isDevelopment,
    isProduction,
  };
}

export const config = createConfig();