# Networking Agent
This is the A2A server for the Networking Agent.

The python version and dependencies for this service are listed in `.python-version` and `pyproject.toml` respectively.

### Implementation
The A2A implementation is based on the official python [sample](https://github.com/google/A2A/tree/main/docs/tutorials/python) by Google (as of 5th of May 2025).

### Running
First, please read the 'Running Merlin' section of the main `README` in the root of this repository, and ensure you've completed the necessary setup.

#### Database Initialisation Script
Cosmos DB for PostgreSQL is used by the agent to store conversation history and relevant agent states. The
database requires initial configuration before the application can run. ​

A setup script has been provided as part of the code to automate this configuration process.​
```bash
cd ./agents/networking_agent
uv run python ./src/networking_agent/setup.py
```

#### Running the Server

This project uses uv as the package and project manager for each MCP server.
```bash
cd ./agents/networking_agent
uv venv
uv sync
uv run ./src/networking_agent/__init__.py
```
