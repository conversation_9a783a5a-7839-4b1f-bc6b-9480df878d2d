
from langgraph.graph import StateGraph, START
from langgraph.graph.graph import CompiledGraph
import os
from langgraph.checkpoint.postgres.aio import AsyncPostgresSaver
from networking_agent.graph.state import State
from networking_agent.graph.nodes import llm_with_tools_node, resource_loader_node, tools_node
from networking_agent.a2a.api_types import Ticket
import logging

def build_base_graph() -> CompiledGraph:
    graph_builder = StateGraph(State)
    graph_builder.add_edge(START, "resource_loader_node")
    graph_builder.add_node("resource_loader_node", resource_loader_node)
    graph_builder.add_node("llm_with_tools_node", llm_with_tools_node)
    graph_builder.add_node("tools_node", tools_node)
    return graph_builder

async def query_graph(prompt: str, thread_id: str):
    async with AsyncPostgresSaver.from_conn_string(os.environ["POSTGRESQL_URL"]) as checkpointer:
        base_graph = build_base_graph()
        agent = base_graph.compile(checkpointer=checkpointer)

        config = {
            "configurable": {
                "thread_id": thread_id
            },
            "recursion_limit": 75
        }

        response = await agent.ainvoke({"messages": prompt}, config=config)
        return response

async def query_ticket(ticket: Ticket, thread_id: str):
    try:
        logging.info(f"Got ticket: {ticket}")
        message = f"### {ticket['title']}\nTicket Number: {ticket['ticket_number']}\n\n{ticket['description']}"
        async with AsyncPostgresSaver.from_conn_string(os.environ["POSTGRESQL_URL"]) as checkpointer:
            base_graph = build_base_graph()
            agent = base_graph.compile(checkpointer=checkpointer)
            config = {
                "configurable": {
                    "thread_id": thread_id
                },
                "recursion_limit": 75
            }
            response = await agent.ainvoke({"messages": message, "ticket": ticket}, config=config)
            return response

    except Exception as e:
        logging.info(f"Error while querying ticket: {e}")
        pass

# if __name__ == "__main__":
#     import uuid
#     import asyncio
#     import sys

#     query = input("Enter a message here: ")
    
#     async def main():
#         thread_id = str(uuid.uuid4())
#         response = await query_graph(query, thread_id)
#         messages = convert_history(response["messages"])
#         print(messages[-1].references)

#     if sys.platform:
#             asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())

#     asyncio.run(main())
