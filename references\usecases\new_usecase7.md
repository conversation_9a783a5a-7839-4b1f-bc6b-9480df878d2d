🔹 User Input:
“Azure Bastion "vnet-identity-npd-mel-01-bastion" can’t connect to my VM "NPDASCYBSTC01-
***********". Could the NSG be the issue?”

🔹 Explanation & Analysis:
NSG on subnet blocks 22/3389 from Bastion subnet.
Retrieve the bastion subnet from the vnet

🔹 Root Cause:
No allow rule for Bastion traffic.

🔹 Terraform Change Required:
Add rule for Bastion subnet IP range.

🔹 File to Edit:
azure-connectivity-non-production/spoke-config/identity-non-production/nsg.yaml

🔹 Updated File:
hcl
Copy
Edit
    nsgs:
      - name: nsg-cyberark-npd-mel-01
        do_not_apply_default_rules: false
        rules:
          - name: "AllowBastionInBound"
            description: ""
            priority: 101
            protocol: "TCP"
            access: "Allow"
            direction: "Inbound"
            source: ["************/26"] 
            source_port: ["*"]
            destination: ["***********/26", "*************/26"]
            destination_port: ["22", "3389"]