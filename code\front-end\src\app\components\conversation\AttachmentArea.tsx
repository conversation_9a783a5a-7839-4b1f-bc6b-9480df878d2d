"use client";

import { useRef, ChangeEvent } from "react";
import Icon from "../../icons/Icon";
import { logger } from "../../utils/logger";
import { AttachmentAreaProps } from "../../types";

export default function AttachmentArea({
  isOpen,
  onToggle,
}: AttachmentAreaProps) {
  const fileInputRef = useRef<HTMLInputElement>(null);

  function handleBrowseClick() {
    fileInputRef.current?.click();
  }

  function handleFileChange(e: ChangeEvent<HTMLInputElement>) {
    const files = e.target.files;
    if (files && files.length > 0) {
      logger.log("Files selected:", files);
      onToggle(); // Close the attachment area
      if (fileInputRef.current) {
        fileInputRef.current.value = "";
      }
    }
  }

  function handleDragOver(e: React.DragEvent<HTMLDivElement>) {
    e.preventDefault();
    e.stopPropagation();
  }

  function handleDrop(e: React.DragEvent<HTMLDivElement>) {
    e.preventDefault();
    e.stopPropagation();

    const files = e.dataTransfer.files;
    if (files && files.length > 0) {
      logger.log("Files dropped:", files);
      onToggle(); // Close the attachment area
    }
  }

  if (!isOpen) return null;

  return (
    <div className="relative bg-gray-100 p-8 animate-slideUp">
      {/* Gradient border at the top of attachment area */}
      <div className="absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-blue-800 via-blue-600 to-blue-300"></div>

      <div className="relative pt-2">
        {/* Close button */}
        <button
          onClick={onToggle}
          className="absolute top-0 right-0 p-2 text-gray-500 hover:text-gray-700 focus:outline-none"
        >
          <Icon name="close" className="w-5 h-5" />
        </button>

        {/* Drag and drop area */}
        <div
          className="border-2 border-dotted border-blue-500 p-8 text-center bg-gray-200 cursor-pointer hover:border-blue-600 transition-colors"
          onDragOver={handleDragOver}
          onDrop={handleDrop}
          onClick={handleBrowseClick}
        >
          <Icon
            name="upload"
            className="mx-auto text-gray-400 mb-4 w-10 h-10"
          />
          <p className="text-gray-700 font-medium mb-2">
            Drag and drop files to upload
          </p>
          <p className="text-gray-500 text-sm mb-4">or</p>
          <button
            type="button"
            className="px-4 py-2 bg-blue-600 text-white hover:bg-blue-700 transition-colors font-medium"
          >
            Browse files
          </button>
          <input
            type="file"
            ref={fileInputRef}
            onChange={handleFileChange}
            className="hidden"
            multiple
          />
        </div>
      </div>
    </div>
  );
}
