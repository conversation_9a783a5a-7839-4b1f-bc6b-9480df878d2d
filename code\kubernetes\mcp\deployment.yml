apiVersion: apps/v1
kind: Deployment
metadata:
  name: networking-mcp
  namespace: networking
  labels:
    app: networking-mcp
    version: v1
spec:
  replicas: 1
  selector:
    matchLabels:
      app: networking-mcp
  template:
    metadata:
      labels:
        app: networking-mcp
        version: v1
    spec:
      containers:
      - name: networking-mcp
        image: acrsbxl5binteg.azurecr.io/agentic-ai/mcp-agent:v28
        command:
        - "fastmcp"
        # - "uv"
        # - "uvicorn"
        args:
        - "run"
        - "/app/src/networking_mcp/__init__.py"
        - "--transport"
        - "sse" 
        ports:
        - containerPort: 8000
          name: http
        envFrom:
        - configMapRef:
            name: networking-agent-config
        - secretRef:
            name: networking-agent-secret
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        # livenessProbe:
        #   httpGet:
        #     path: /health 
        #     port: 8000
        #   initialDelaySeconds: 30
        #   periodSeconds: 10
        #   timeoutSeconds: 5
        #   failureThreshold: 3
        # readinessProbe:
        #   httpGet:
        #     path: /ready 
        #     port: 8000
        #   initialDelaySeconds: 5
        #   periodSeconds: 5
        #   timeoutSeconds: 3
        #   failureThreshold: 3
        securityContext:
          allowPrivilegeEscalation: false
          runAsNonRoot: true
          runAsUser: 1000
          capabilities:
            drop:
            - ALL
      imagePullSecrets:
      - name: acr-secret 
      restartPolicy: Always