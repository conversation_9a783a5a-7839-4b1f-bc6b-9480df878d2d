trigger: none

variables:
  vmImageName: 'ubuntu-latest'
  environmentName: 'agenticai'
  
stages:
- stage: Build
  displayName: Build stage
  jobs:
  - job: Build
    displayName: Build
    pool:
       name: 'aks-ado-agent-npd'
       #vmImage: $(vmImageName) 
       name: 'aks-ado-agent-npd'
       #vmImage: $(vmImageName) 
    steps:
  #  - task: UsePythonVersion@0
  #    inputs:
  #      versionSpec: '3.12'
  #      disableDownloadFromRegistry: true
  #      architecture: 'x64'
  #  - task: UsePythonVersion@0
  #    inputs:
  #      versionSpec: '3.12'
  #      disableDownloadFromRegistry: true
  #      architecture: 'x64'
    - script: |
        python -m pip install --upgrade pip
        pip install uv
      displayName: 'Install uv dependencies'  
    - script: |
        set -e  # Stops execution on error
        cd code/agents/networking_agent
        uv venv || { echo "Error: Failed to create virtual environment"; exit 1; }
        uv sync || { echo "Error: Failed to sync dependencies"; exit 1; }
        uv build  || { echo "Error: Build failed"; exit 1; }
        tar -xvzf dist/networking_agent-0.1.0.tar.gz
        zip -r webapp.zip networking_agent-0.1.0/*
        tar -xvzf dist/networking_agent-0.1.0.tar.gz
        zip -r webapp.zip networking_agent-0.1.0/*
        #uv run ./src/networking_agent/__init__.py
      displayName: 'Build the project'

    - task: PublishBuildArtifacts@1
      inputs:
        PathtoPublish: '$(Pipeline.Workspace)/s/code/agents/networking_agent/webapp.zip'
        PathtoPublish: '$(Pipeline.Workspace)/s/code/agents/networking_agent/webapp.zip'
        ArtifactName: 'drop'
    - task: AzureWebApp@1
      displayName: 'Deploy Webapp'
      inputs:
        appName: 'wa-devopsai-npd-mel-a2a'
        azureSubscription: 'ado-management-federate-sc-management-non-production-Agentic-AI'
        package: '$(Pipeline.Workspace)/s/code/agents/networking_agent/webapp.zip'
        appName: 'wa-devopsai-npd-mel-a2a'
        azureSubscription: 'ado-management-federate-sc-management-non-production-Agentic-AI'
        package: '$(Pipeline.Workspace)/s/code/agents/networking_agent/webapp.zip'
        appType: webAppLinux
        startUpCommand: 'apt-get update && apt-get install -y git && cd networking_agent-0.1.0 && pip install uv && uv sync && uv run ./src/networking_agent/__init__.py --port 8000 --host 0.0.0.0'