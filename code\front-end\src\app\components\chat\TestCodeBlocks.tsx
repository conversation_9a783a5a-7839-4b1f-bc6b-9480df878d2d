import ChatMessage from "../ChatMessage";

export default function TestCodeBlocks() {
  const dummyMessages = [
    {
      id: "1",
      type: "human" as const,
      content: "Can you show me a Python function to calculate fibonacci numbers?",
      timestamp: new Date(),
    },
    {
      id: "2",
      type: "ai" as const,
      content: `Here's a Python function to calculate Fibonacci numbers:

\`\`\`python
def fibonacci(n):
    """Calculate the nth Fibonacci number using recursion."""
    if n <= 1:
        return n
    return fibonacci(n - 1) + fibonacci(n - 2)

# Example usage
for i in range(10):
    print(f"F({i}) = {fibonacci(i)}")
\`\`\`

And here's a more efficient iterative version:

\`\`\`python
def fibonacci_iterative(n):
    """Calculate the nth Fibonacci number iteratively."""
    if n <= 1:
        return n
    
    a, b = 0, 1
    for _ in range(2, n + 1):
        a, b = b, a + b
    
    return b

# Test the function
result = fibonacci_iterative(10)
print(f"The 10th Fibonacci number is: {result}")
\`\`\`

You can also use inline code like \`fibonacci(5)\` in your text.`,
      timestamp: new Date(),
      intermediate_steps: [
        {
          title: "Understanding the request",
          content: "User wants a Python function for Fibonacci numbers",
        },
        {
          title: "Choosing approach",
          content: "Providing both recursive and iterative solutions",
        },
      ],
    },
    {
      id: "3",
      type: "human" as const,
      content: "Now show me a JSON example",
      timestamp: new Date(),
    },
    {
      id: "4",
      type: "ai" as const,
      content: `Here's a JSON example with user data:

\`\`\`json
{
  "users": [
    {
      "id": 1,
      "name": "John Doe",
      "email": "<EMAIL>",
      "age": 30,
      "address": {
        "street": "123 Main St",
        "city": "New York",
        "zipCode": "10001"
      },
      "hobbies": ["reading", "swimming", "coding"],
      "isActive": true
    },
    {
      "id": 2,
      "name": "Jane Smith",
      "email": "<EMAIL>",
      "age": 25,
      "address": {
        "street": "456 Oak Ave",
        "city": "Los Angeles",
        "zipCode": "90210"
      },
      "hobbies": ["painting", "hiking"],
      "isActive": false
    }
  ],
  "totalCount": 2,
  "lastUpdated": "2024-01-15T10:30:00Z"
}
\`\`\`

And here's some JavaScript to work with this data:

\`\`\`javascript
// Fetch and process user data
async function fetchUsers() {
  try {
    const response = await fetch('/api/users');
    const data = await response.json();
    
    // Filter active users
    const activeUsers = data.users.filter(user => user.isActive);
    
    console.log('Active users:', activeUsers);
    return activeUsers;
  } catch (error) {
    console.error('Error fetching users:', error);
  }
}

// Call the function
fetchUsers();
\`\`\`

You can access individual properties like \`user.name\` or \`user.address.city\`.`,
      timestamp: new Date(),
      intermediate_steps: [],
    },
  ];

  return (
    <div className="max-w-4xl mx-auto p-4 space-y-4">
      <h1 className="text-2xl font-bold mb-6">Code Block Test</h1>
      {dummyMessages.map((message) => (
        <ChatMessage
          key={message.id}
          id={message.id}
          type={message.type}
          content={message.content}
          timestamp={message.timestamp}
          intermediate_steps={message.intermediate_steps}
        />
      ))}
    </div>
  );
}
