# module "naming" {
#     source  = "app.terraform.io/unisuper/naming/azurerm"
#     version = "~> 2.0.3"
#     unique_id   = "ai-agent"
#     location    = var.location
#     environment = var.environment
# }

resource "azurerm_kubernetes_cluster" "aks" {
    name                = module.naming.aks_cluster_name
    location            = module.resource_group.location
    resource_group_name = module.resource_group.name
    dns_prefix          = module.naming.aks_cluster_name

    kubernetes_version  = data.azurerm_kubernetes_service_versions.aks_current.latest_version # this is to initial release
    #kubernetes_version  = "1.26.6"

    automatic_upgrade_channel   = "stable"
    maintenance_window_auto_upgrade {
        frequency   = "RelativeMonthly"
        interval    = 2
        duration    = 4
        day_of_week = "Sunday"
        week_index  = "Second"
        start_time  = "00:00"
        utc_offset  = "+10:00"
    }

    default_node_pool {
        name       = "pool1npdmel"
        node_count = 1
        vm_size    = "Standard_D4S_v3"
        auto_scaling_enabled = true
        max_count           = 2
        min_count           = 1
        max_pods            = 30
        os_disk_size_gb     = 100
        os_disk_type        = "Ephemeral"
        vnet_subnet_id      = data.azurerm_subnet.aks-snet.id
        temporary_name_for_rotation = "temp"
        # node_labels = {
        #   "nodepool-type" = "system"
        #   "environment"   = module.naming.environment_tag_value
        #   "nodepoolos"    = "linux"
        #   "project"       = "default"
        # }
    }

    identity {
        type = "SystemAssigned"
    }

    # oms_agent {
    #     msi_auth_for_monitoring_enabled = true
    #     log_analytics_workspace_id      = local.law_id
    # }

    # azure_active_directory_role_based_access_control {
    #     managed                = true
    #     #admin_group_object_ids = [data.azuread_group.aks_administrators.id, "********-70fb-4f1b-9ffa-d166c671dd8b"]
    #     admin_group_object_ids = ["********-70fb-4f1b-9ffa-d166c671dd8b"]
    # }

    linux_profile {
        admin_username = "usmadmin"
        ssh_key {
            key_data = data.azurerm_key_vault_key.linux_ssh_key.public_key_openssh
        }
    }

    network_profile {
        load_balancer_sku = "standard"
        network_plugin    = "azure"
        network_plugin_mode = "overlay"
        service_cidr = "***********/21"
        dns_service_ip = "************"
        pod_cidr = "***********/21"
        outbound_type = "userDefinedRouting"
    }
    lifecycle {
        ignore_changes = [default_node_pool]
    }
    tags = {
        Department          = "Enterprise Infrastructure and Cloud"
        Environment         =  module.naming.environment_tag_value
        Platform            = "AI"
        Project             = "AI-DevOps"
        terraform-workspace = terraform.workspace
    }
}

resource "azurerm_key_vault_access_policy" "kv_ap_aks" {
    key_vault_id    = azurerm_key_vault.default.id
    tenant_id       = data.azurerm_client_config.current.tenant_id
    object_id       = azurerm_kubernetes_cluster.aks.kubelet_identity[0].object_id

    key_permissions = [
        "Get",
        "List"
    ]

    secret_permissions = [
        "Get",
        "List"
    ]
}

resource "azurerm_key_vault_access_policy" "kv_ap_venafi" {
    key_vault_id    = azurerm_key_vault.default.id
    tenant_id       = data.azurerm_client_config.current.tenant_id
    object_id       = data.azuread_service_principal.venafi-spn.object_id

    certificate_permissions = [
        "Get",
        "List",
        "Create",
        "Update",
        "Import"
    ]

    secret_permissions = [
        "Get"
    ]
}

# to be discussed with team - terraform to manage helm releases?
# moved to helm-charts for single pane management
# resource "helm_release" "nginx_ingress" {
#   name       = "ingress-nginx"
#   repository = "https://kubernetes.github.io/ingress-nginx"
#   chart      = "ingress-nginx"
#   version    = "4.12.2"

#   namespace        = "ingress-nginx"
#   create_namespace = true

#   values = [
#     <<EOF
# controller:
#   replicaCount: 1
#   service:
#     type: LoadBalancer
#     annotations:
#       service.beta.kubernetes.io/azure-load-balancer-ipv4: *************
#       service.beta.kubernetes.io/azure-load-balancer-internal: "true"
# #   livenessProbe:
# #     httpGet:
# #       path: /livez
# #       port: http
# #     initialDelaySeconds: 30
# #     periodSeconds: 10
# #   readinessProbe:
# #     httpGet:
# #       path: /readyz
# #       port: http
# #     initialDelaySeconds: 5
# #     periodSeconds: 10
# EOF
#   ]
# }

resource "azurerm_container_registry" "acr" {
    name                     = module.naming.container_registry_name
    resource_group_name      = module.naming.resource_group_name
    location                 = module.naming.location
    sku                      = "Standard"
    admin_enabled            = false
    tags                     = {
        Department          = "Enterprise Infrastructure and Cloud"
        Environment         =  module.naming.environment_tag_value
        Platform            = "AI"
        Project             = "AI-DevOps"
        terraform-workspace = terraform.workspace
    }
    # DevOps team believe we don't need Premium SKU and georeplication moving to standard sku
    # georeplications {
    #     location                = "Australia East"
    #     zone_redundancy_enabled = true
    #     tags                    = {}
    # }

    # think about this seriously
    # retention_policy {
    #     days = 90
    #     enabled = true
    # }
}

resource "azurerm_role_assignment" "acr_rbac" {
  scope                = azurerm_container_registry.acr.id
  role_definition_name = "AcrPull"
  principal_id         = azurerm_kubernetes_cluster.aks.kubelet_identity[0].object_id
}