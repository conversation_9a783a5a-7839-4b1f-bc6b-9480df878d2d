"use client";

import { ChangeEvent, FormEvent } from "react";
import Icon from "../../icons/Icon";

interface MessageInputBoxProps {
  query: string;
  setQuery: (query: string) => void;
  isLoading: boolean;
  isAttachmentAreaOpen: boolean;
  onToggleAttachment: () => void;
  onSubmit: (query: string) => void;
}

export default function MessageInputBox({
  query,
  setQuery,
  isLoading,
  isAttachmentAreaOpen,
  onToggleAttachment,
  onSubmit,
}: MessageInputBoxProps) {
  function handleChange(e: ChangeEvent<HTMLInputElement>) {
    setQuery(e.target.value);
  }

  function handleSubmit(e: FormEvent) {
    e.preventDefault();
    if (!query.trim()) return;

    onSubmit(query);
    setQuery("");
  }

  return (
    <div className="relative bg-white">
      {/* Gradient border - only show when attachment area is closed */}
      {!isAttachmentAreaOpen && (
        <div className="absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-blue-800 via-blue-600 to-blue-300"></div>
      )}
      <div className="pt-1 pb-1 px-1">
        <form onSubmit={handleSubmit} className="relative">
          {/* Input field */}
          <input
            type="text"
            value={query}
            onChange={handleChange}
            className="w-full px-6 py-3 pr-32 focus:outline-none bg-white"
            placeholder="Send a message"
          />

          {/* Icons container */}
          <div className="absolute right-2 top-1/2 transform -translate-y-1/2 flex items-center space-x-1">
            {/* Send button */}
            <button
              type="submit"
              disabled={isLoading || !query.trim()}
              className="bg-gray-900 text-white rounded-full p-2 hover:bg-gray-800 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200 shadow-sm"
            >
              <Icon name="arrow-up" className="w-[18px] h-[18px]" />
            </button>

            {/* Attachment button */}
            <button
              type="button"
              onClick={onToggleAttachment}
              className={`p-2 rounded-full ${
                isAttachmentAreaOpen
                  ? "bg-blue-100 text-blue-600"
                  : "text-gray-500 hover:bg-gray-100"
              } transition-colors`}
            >
              <Icon name="attachment" className="w-[18px] h-[18px]" />
            </button>

            {/* Three-dot menu */}
            <button
              type="button"
              className="p-2 rounded-full text-gray-500 hover:bg-gray-100 transition-colors"
            >
              <Icon name="three-dots-horizontal" className="w-[18px] h-[18px]" />
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
