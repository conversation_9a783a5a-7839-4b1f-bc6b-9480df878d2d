# Network Infrastructure Repository Guidelines v4 - Enhanced AI Agent Guide

## Overview

This guide provides comprehensive instructions for AI agents working with UniSuper's Azure DevOps network infrastructure repositories. It includes advanced patterns, troubleshooting workflows, and operational procedures based on the actual Azure Cloud Adoption Framework (CAF) landing zone implementation and Infrastructure-as-Code (IaC) repository structure across Production (unisuper.lan) and Non-Production (devops.lan) domains.

---

## Repository Architecture Summary

### Azure DevOps Organization Structure

UniSuper's network infrastructure is managed through two primary Azure DevOps organizations:

1. **Unisuper** - Application team repositories
2. **Unisuper-Infrastructure** - Platform team repositories (Primary focus for network engineering)

### Three-Repository Network Strategy

UniSuper implements network infrastructure through three specialized repositories:

#### **1. azure-connectivity-non-production (YAML-Driven Hub-and-Spoke)**
- **Purpose**: Platform landing zone hub-and-spoke topology
- **Configuration Method**: YAML-driven with Terraform module processing
- **Module Source**: Private modules from `app.terraform.io/unisuper/`
- **Coverage**: Central hub infrastructure, spoke peering, shared services

#### **2. azure-usm-non-production-network (Legacy Import Management)**  
- **Purpose**: Legacy resource integration using aztfexport methodology
- **Configuration Method**: Import-focused with resource group separation
- **Organization**: Separated by resource type (NSGs/, RouteTables/, Vnets/)

#### **3. azure-next-gen-firewall-vwan (Virtual WAN Architecture)**
- **Purpose**: Next-generation Virtual WAN with Cloud NGFW
- **Configuration Method**: Direct Terraform HCL (not YAML-driven)
- **Regional Structure**: nonprod-mel/, prod-mel/, prod-syd/

### Platform Team Repository Organization (Unisuper-Infrastructure)

#### **Azure DevOps Projects to Landing Zone Mapping**

| Azure DevOps Project | Landing Zone | Environment | Purpose | Repository Examples |
|----------------------|--------------|-------------|---------|-------------------|
| **Connectivity** | Connectivity | Production, Non-Production | Core network services, ExpressRoute, Azure Firewall, SD-WAN | azure-connectivity-production, azure-connectivity-non-production |
| **Identity** | Identity | Production, Non-Production | Identity, Cyberark, Active Directory | azure-identity-production, azure-identity-non-production |
| **Management** | Management | Production, Non-Production | Monitoring, Logging, Security services, DevOps | azure-management-production, azure-management-non-production |
| **Core-Administration** | Core-Administration | Production, Non-Production | RPA, Power Automate | azure-core-admin-production, azure-core-admin-non-production |
| **Corporate** | Corporate | Production, Non-Production | Abacus, finance applications, shared services | azure-corporate-production, azure-corporate-non-production |
| **Digital** | Digital | Production, Non-Production | Sitecore, customer portals, member services | azure-digital-production, azure-digital-non-production |
| **MDP-Infra** | Data-Management | Production, Non-Production | Data platform, analytics, ERMS | azure-mdp-production, azure-mdp-non-production |
| **Investments** | Investments | Production, Non-Production | Investment management applications | azure-investments-production, azure-investments-non-production |

#### **Specialized Infrastructure Repositories**

| Azure DevOps Project | Purpose | Repository Examples | Scope |
|----------------------|---------|-------------------|-------|
| **GCP** | Google Cloud Platform | gcp-network-production, gcp-network-non-production | All GCP networking services |
| **GCVE** | Google Cloud VMware Engine | gcve-network-production, gcve-network-non-production | vSphere, NSX-T networking |
| **Palo-Alto-Firewall-Management** | Firewall Management | palo-alto-management, firewall-policies | All Palo Alto firewalls |
| **Terraform-Modules** | Infrastructure Modules | terraform-network-modules, terraform-security-modules | Reusable IaC modules |

---

## Repository 1: azure-connectivity-non-production (YAML-Driven)

### Core Architecture
**Purpose**: Manages foundational Hub-and-Spoke network topology using YAML-driven configuration
**Configuration Method**: YAML files processed by Terraform with UniSuper private modules
**Module Source**: `app.terraform.io/unisuper/` private module registry
**Coverage**: Central hub infrastructure, spoke peering, NSGs, route tables

### Enhanced File Structure
```
azure-connectivity-non-production/
├── spoke/
│   ├── config/
│   │   ├── data-management-non-production/
│   │   │   ├── vnet.yaml              # VNet definitions
│   │   │   ├── subnet.yaml            # Subnet configurations with delegations
│   │   │   ├── nsg.yaml               # Network Security Groups
│   │   │   ├── route_table.yaml       # User-Defined Routes
│   │   │   └── resource_group.yaml    # Resource group definitions
│   │   ├── digital-non-production/
│   │   ├── corporate-non-production/
│   │   ├── investments-non-production/
│   │   ├── identity-non-production/
│   │   ├── management-non-production/
│   │   └── core-administration-non-production/
│   ├── modules/
│   │   └── config/                    # YAML processing module
│   ├── main.tf                        # Terraform processing engine
│   ├── provider.tf                    # Multi-subscription providers
│   ├── locals.tf                      # Local value calculations
│   └── pipeline.yaml                  # Azure DevOps automation
├── hub/                               # Hub infrastructure (future)
└── modules/                           # Shared modules
```

### Actual Terraform Module Usage

#### Main Processing Engine
```terraform
# Main terraform processing with UniSuper modules
module "config" {
  source = "./modules/config"

  subscription            = var.subscription
  network_rg_naming_map  = local.network_rg_naming_map
  vnet_naming_map        = local.vnet_naming_map
}

module "naming_network_rg" {
  source  = "app.terraform.io/unisuper/naming/azurerm"
  version = "1.0.8"

  for_each = {
    for key, value in module.config.naming_rg : "${value.unique_id}-${value.environment}-${value.location}" => value
  }

  unique_id   = each.value.unique_id
  environment = each.value.environment
  location    = each.value.location
}

module "resource_group" {
  source  = "app.terraform.io/unisuper/resource-group/azurerm"
  version = "1.0.5"

  for_each = {
    for key, value in module.config.network_rg_map : "${value.resource_group_name}" => value
  }

  name     = each.value.resource_group_name
  location = each.value.location

  tags = {
    Department          = each.value.department_for_tag
    Environment         = each.value.environment_for_tag
    Platform            = each.value.platform_for_tag
    Project             = each.value.project_for_tag
    terraform-workspace = terraform.workspace
  }
}
```

#### NSG Module Implementation
```terraform
module "nsg" {
  source  = "app.terraform.io/unisuper/nsg/azurerm"
  version = "1.0.1"

  for_each = {
    for key, value in module.config.nsg_map : "${value.nsg_name}" => value
  }

  name                = each.value.nsg_name
  resource_group_name = each.value.resource_group_name
  location            = local.nsg_rg_location[each.value.resource_group_name]
  security_rules      = each.value.security_rules != null ? each.value.security_rules : []

  tags = local.nsg_rg_tags[each.value.resource_group_name]
}
```

#### VNet and Subnet Module Implementation
```terraform
module "vnet" {
  source  = "app.terraform.io/unisuper/vnet/azurerm"
  version = "1.0.2"

  for_each = {
    for key, value in module.config.vnet_map : "${value.virtual_network_name}" => value
  }

  name                = each.value.virtual_network_name
  location            = each.value.location
  resource_group_name = each.value.resource_group_name
  address_space       = each.value.cidr
  dns_servers         = each.value.dns_servers

  tags = module.resource_group[each.value.resource_group_name].tags

  depends_on = [module.resource_group]
}

module "subnet" {
  source  = "app.terraform.io/unisuper/subnet/azurerm"
  version = "v1.0.1"

  for_each = {
    for key, value in module.config.subnet_map : "${value.subnet_name}" => value
  }

  name                     = each.value.subnet_name
  virtual_network_name     = each.value.vnet_name
  resource_group_name      = each.value.resource_group_name
  address_prefixes         = each.value.address_prefixes
  delegation               = each.value.delegation
  service_endpoints        = each.value.service_endpoints

  private_endpoint_network_policies                  = each.value.private_endpoint_network_policies
  private_link_service_network_policies_enabled      = each.value.private_link_service_network_policies_enabled

  network_security_group_association = {
    enable = each.value.nsg_name != null ? true : false
    id     = each.value.nsg_name != null ? module.nsg[each.value.nsg_name].id : null
  }

  route_table_association = {
    enable = each.value.route_table_name != null ? true : false
    id     = each.value.route_table_name != null ? module.route_table[each.value.route_table_name].id : null
  }

  depends_on = [module.vnet, module.nsg]
}
```

### Data Management Landing Zone Environment Structure

Based on actual implementation, the Data Management landing zone supports multiple granular environments:

#### Environment-Specific Subnet Patterns
```yaml
vnets:
  "vnet-mdp-npd-mel-01":
    resourcegroup: "rg-mdp-network-npd-mel-01"
    subnets:
      # Development Environment
      - name: snet-mdp-dev-mel-app-01
        address_prefixes: ["***********/26"]
        nsg_name: nsg-mdp-dev-mel-01
        route_table_name: rt-mdp-npd-standard

      # Test Environment 1
      - name: snet-mdp-tst1-mel-app-01
        address_prefixes: ["***********/26"]
        nsg_name: nsg-mdp-tst1-mel-01
        route_table_name: rt-mdp-npd-standard

      # Test Environment 2
      - name: snet-mdp-tst2-mel-app-01
        address_prefixes: ["***********/26"]
        nsg_name: nsg-mdp-tst2-mel-01
        route_table_name: rt-mdp-npd-standard

      # System Integration Test 1
      - name: snet-mdp-sit1-mel-app-01
        address_prefixes: ["***********/26"]
        nsg_name: nsg-mdp-sit1-mel-01
        route_table_name: rt-mdp-npd-standard

      # System Integration Test 2
      - name: snet-mdp-sit2-mel-app-01
        address_prefixes: ["***********/26"]
        nsg_name: nsg-mdp-sit2-mel-01
        route_table_name: rt-mdp-npd-standard

      # Regression Environment 1
      - name: snet-mdp-rgr1-mel-app-01
        address_prefixes: ["***********/26"]
        nsg_name: nsg-mdp-rgr1-mel-01
        route_table_name: rt-mdp-npd-standard

      # Regression Environment 2
      - name: snet-mdp-rgr2-mel-app-01
        address_prefixes: ["***********/26"]
        nsg_name: nsg-mdp-rgr2-mel-01
        route_table_name: rt-mdp-npd-standard

      # Non-Production Master
      - name: snet-mdp-npd-mel-app-01
        address_prefixes: ["***********/26"]
        nsg_name: nsg-mdp-npd-mel-01
        route_table_name: rt-mdp-npd-standard
```

#### Databricks Delegation Patterns
```yaml
# Databricks workspace delegation example
- name: snet-mdp-dev-mel-databricks-ctl-01
  address_prefixes: ["************/26"]
  nsg_name: nsg-mdp-dev-mel-databricks-ctl-01
  route_table_name: rt-mdp-npd-to-databricks
  service_endpoints:
    - "Microsoft.Storage"
    - "Microsoft.Sql"
  delegation:
    "dev_dbw_delegation_public":
      "Microsoft.Databricks/workspaces":
        - "Microsoft.Network/virtualNetworks/subnets/join/action"
        - "Microsoft.Network/virtualNetworks/subnets/prepareNetworkPolicies/action"
        - "Microsoft.Network/virtualNetworks/subnets/unprepareNetworkPolicies/action"
```

### NSG Configuration with Default Rules
```yaml
mdp-vnet:
  "default":
    rules:
      # Default rules applied to all NSGs (priority 3000-4096)
      - name: "AllowADOInBound"
        description: "Allow inbound connectivity from ADO agents"
        priority: 3000
        protocol: "TCP"
        access: "Allow"
        direction: "Inbound"
        source: ["**********/24"]
        destination: ["VirtualNetwork"]
        destination_port: ["443", "1433"]

      - name: "DenyAnyInboundfromPrivate"
        description: "Deny all source from private IP to any destination"
        priority: 4096
        protocol: "*"
        access: "Deny"
        direction: "Inbound"
        source: ["10.0.0.0/8", "**********/12", "***********/16"]
        destination: ["VirtualNetwork"]
        destination_port: ["*"]

  "rg-mdp-network-npd-mel-01":
    nsgs:
      - name: nsg-mdp-dev-mel-01
        rules:
          # Environment-specific rules (priority 100-199)
          - name: "AllowInBoundFromMDPNpdSubnet"
            priority: 100
            protocol: "*"
            access: "Allow"
            direction: "Inbound"
            source: ["***********/24"]
            destination: ["***********/26", "*************/26"]
            destination_port: ["*"]

      - name: nsg-mdp-dev-mel-databricks-ctl-01
        do_not_apply_default_rules: true  # Databricks requires specific rule sets
```

---

## Repository 2: azure-usm-non-production-network (Legacy Integration)

### Import-Focused Architecture
**Purpose**: Integration and migration planning for legacy USM subscriptions using aztfexport
**Configuration Method**: Import-based approach with resource group separation
**Organization**: Separated by resource type for systematic migration

### Actual File Structure
```
azure-usm-non-production-network/
├── NSGs/
│   ├── rg-connectivity-npd-01/        # NSGs by resource group
│   ├── rg-npd-commvault/
│   ├── rg-npd-firewall/
│   ├── rg-npd-network/
│   ├── rg-npd-mel-devenv-integ/
│   ├── rg-npd-enterprise-core-monitoring/
│   └── rg-tst-qmv/
├── RouteTables/
│   └── [resource-group-directories]/   # Route tables by resource group
├── Vnets/
│   └── [resource-group-directories]/   # VNets by resource group
├── provider.aztfexport.tf              # aztfexport provider configuration
├── terraform.aztfexport.tf             # aztfexport terraform configuration
└── README.md                           # Import methodology documentation
```

### aztfexport Integration Patterns
```terraform
# aztfexport provider configuration
terraform {
  required_providers {
    azurerm = {
      source  = "hashicorp/azurerm"
      version = "~>3.0"
    }
  }
}

provider "azurerm" {
  features {}
}
```

---

## Repository 3: azure-next-gen-firewall-vwan (Virtual WAN Architecture)

### Next-Generation Architecture
**Purpose**: Virtual WAN infrastructure with Palo Alto Cloud NGFW
**Configuration Method**: Direct Terraform HCL (not YAML-driven)
**Regional Structure**: Environment and region-based directories

### Actual File Structure
```
azure-next-gen-firewall-vwan/
├── nonprod-mel/
│   ├── vwan.tf                        # Virtual WAN and hub configuration
│   ├── cloudngfw.tf                   # Palo Alto Cloud NGFW
│   ├── spoke-peerings.tf              # Virtual hub connections
│   ├── connect-erc.tf                 # ExpressRoute connections
│   ├── connect-sdwan.tf               # SD-WAN integration
│   ├── main.tf                        # Main configuration
│   ├── variables.tf                   # Variable definitions
│   └── providers.tf                   # Provider configuration
├── prod-mel/                          # Production Melbourne
├── prod-syd/                          # Production Sydney
└── README.md                          # Virtual WAN documentation
```

### Virtual Hub Connection Implementation
```terraform
# Actual spoke peering configurations
resource "azurerm_virtual_hub_connection" "npd-mdp" {
  name                      = "vhc-npd-mdp"
  virtual_hub_id            = azurerm_virtual_hub.vwan_hub.id
  remote_virtual_network_id = "/subscriptions/1b0fd77c-22a6-4d82-8167-36272c965856/resourceGroups/rg-mdp-network-npd-mel-01/providers/Microsoft.Network/virtualNetworks/vnet-mdp-npd-mel-01"
}

resource "azurerm_virtual_hub_connection" "npd-finance" {
  name                      = "vhc-npd-finance"
  virtual_hub_id            = azurerm_virtual_hub.vwan_hub.id
  remote_virtual_network_id = "/subscriptions/3b717e16-6a68-4789-b29c-ccfb9a3663a4/resourceGroups/rg-finance-non-prod-001/providers/Microsoft.Network/virtualNetworks/vnet-finance-non-prod-001"
}

resource "azurerm_virtual_hub_connection" "npd-management" {
  name                      = "vhc-npd-management"
  virtual_hub_id            = azurerm_virtual_hub.vwan_hub.id
  remote_virtual_network_id = "/subscriptions/0290fa34-a3a4-484a-8ca5-b898c2e52fa8/resourceGroups/rg-management-network-npd-mel-01/providers/Microsoft.Network/virtualNetworks/vnet-management-network-npd-mel-01"
}
```

---

## Advanced Troubleshooting Workflows

### Repository-Based Troubleshooting Framework

#### Step 1: Repository Identification
**Landing Zone Mapping:**
- **Platform Issues**: Connectivity, Identity, or Management project repositories
- **Application Issues**: Business unit specific repositories (MDP-Infra, Digital, Corporate, etc.)
- **Legacy Issues**: USM production/non-production repositories
- **Security Issues**: Palo-Alto-Firewall-Management repository

#### Step 2: Configuration Analysis Approach
**For Connectivity Repository (Platform):**
1. Check hub VNet configuration in `hub/networking/main.tf`
2. Verify Azure Firewall rules in `hub/firewall/firewall-rules.tf`
3. Validate spoke peering configurations in `peering/spoke-peerings.tf`
4. Review ExpressRoute/VPN settings in respective directories

**For Application Landing Zone Repositories:**
1. Check spoke VNet configuration in `networking/vnets/main.tf`
2. Verify NSG rules in `networking/security/nsgs.tf`
3. Validate route tables in `networking/routing/route-tables.tf`
4. Review private endpoints in `networking/private-endpoints/`

#### Step 3: Cross-Repository Dependencies
**Common Dependency Patterns:**
1. **Hub-to-Spoke**: Connectivity repository defines hub; application repositories define spokes
2. **DNS Integration**: Connectivity repository manages private DNS zones; application repositories create private endpoints
3. **Security Policies**: Firewall rules in connectivity repository; NSG rules in application repositories
4. **Legacy Integration**: USM repositories contain legacy resources; CAF repositories define migration targets

---

## Operational Procedures and Best Practices

### Azure DevOps Pipeline Integration

#### Pipeline Structure for Network Changes
```yaml
# Azure DevOps pipeline for network infrastructure
trigger:
  branches:
    include:
    - main
    - develop
  paths:
    include:
    - networking/*
    - security/*
    - peering/*

variables:
  - group: terraform-credentials
  - name: TF_VERSION
    value: '1.5.0'

stages:
- stage: Validate
  displayName: 'Validate Infrastructure'
  jobs:
  - job: TerraformValidate
    displayName: 'Terraform Validation'
    steps:
    - task: TerraformInstaller@0
      displayName: 'Install Terraform'
      inputs:
        terraformVersion: '$(TF_VERSION)'
    
    - task: TerraformTaskV2@2
      displayName: 'Terraform Init'
      inputs:
        provider: 'azurerm'
        command: 'init'
        backendServiceArm: '$(AZURE_SERVICE_CONNECTION)'
        backendAzureRmResourceGroupName: '$(TF_STATE_RG)'
        backendAzureRmStorageAccountName: '$(TF_STATE_SA)'
        backendAzureRmContainerName: '$(TF_STATE_CONTAINER)'
        backendAzureRmKey: '$(TF_STATE_KEY)'
    
    - task: TerraformTaskV2@2
      displayName: 'Terraform Validate'
      inputs:
        provider: 'azurerm'
        command: 'validate'
    
    - task: TerraformTaskV2@2
      displayName: 'Terraform Plan'
      inputs:
        provider: 'azurerm'
        command: 'plan'
        environmentServiceNameAzureRM: '$(AZURE_SERVICE_CONNECTION)'

- stage: Deploy
  displayName: 'Deploy Infrastructure'
  condition: and(succeeded(), eq(variables['Build.SourceBranch'], 'refs/heads/main'))
  jobs:
  - deployment: DeployInfrastructure
    displayName: 'Deploy Network Infrastructure'
    environment: 'production'
    strategy:
      runOnce:
        deploy:
          steps:
          - task: TerraformTaskV2@2
            displayName: 'Terraform Apply'
            inputs:
              provider: 'azurerm'
              command: 'apply'
              environmentServiceNameAzureRM: '$(AZURE_SERVICE_CONNECTION)'
```

### Change Management for CAF Landing Zones

#### Pre-Change Analysis Checklist
1. **Landing Zone Impact**: Identify which CAF landing zones are affected
2. **Cross-Repository Dependencies**: Check for dependencies on other repositories
3. **CIDR Conflicts**: Verify no IP address conflicts with existing allocations
4. **Security Boundaries**: Ensure changes don't violate security policies
5. **Legacy Integration**: Consider impact on USM subscription migration

#### Repository-Specific Change Procedures

**For Connectivity Repository Changes:**
1. **Impact Assessment**: Changes affect all spoke landing zones
2. **Hub Resource Updates**: Azure Firewall, ExpressRoute, VPN gateway changes
3. **Peering Modifications**: Updates to spoke peering configurations
4. **DNS Changes**: Private DNS zone and forwarder updates
5. **Testing**: Validate in non-production environment first

**For Application Landing Zone Changes:**
1. **Spoke-Specific Impact**: Changes typically isolated to single business unit
2. **VNet Configuration**: Updates to spoke VNet and subnet configurations
3. **Security Rule Changes**: NSG and route table modifications
4. **Private Endpoint Updates**: Changes to Azure PaaS connectivity
5. **Cross-Spoke Testing**: Validate communication with other landing zones

---

## AI Agent Operational Guidelines

### Repository-Specific Approaches

#### When Working with Connectivity Repository:
1. **Hub-Centric Thinking**: Understand that changes affect all spokes
2. **Central Security**: Azure Firewall rules impact all landing zones
3. **Gateway Management**: ExpressRoute and VPN changes affect hybrid connectivity
4. **DNS Centralization**: Private DNS changes affect all spoke resolution
5. **Peering Coordination**: Hub peering changes require spoke repository updates

#### When Working with Application Landing Zone Repositories:
1. **Business Unit Focus**: Understand specific business requirements
2. **Landing Zone Isolation**: Changes typically don't affect other landing zones
3. **Standard Patterns**: Use established patterns from other application landing zones
4. **Private Endpoint Strategy**: Implement private endpoints for PaaS connectivity
5. **Cross-Landing Zone Communication**: Configure appropriate NSG and routing rules

#### When Working with Legacy Integration Repositories:
1. **Migration Focus**: Understand migration pathway to CAF landing zones
2. **CIDR Management**: Prevent conflicts during migration phases
3. **Gradual Transition**: Plan phased migration approach
4. **Dependency Mapping**: Understand legacy service dependencies
5. **Compliance Maintenance**: Ensure security standards during migration

### Quality Assurance Standards

#### Before Every Change:
1. **Repository Exploration**: Search through all relevant repositories for patterns
2. **Configuration Validation**: Compare with established templates and standards
3. **Cross-Repository Impact**: Consider dependencies on other landing zones
4. **Security Compliance**: Ensure changes maintain security posture
5. **CIDR Validation**: Prevent IP address conflicts across environments

#### Solution Requirements:
1. **Complete Configurations**: Provide full terraform configurations with all required resources
2. **Variable Definitions**: Include appropriate variable declarations and defaults
3. **Tagging Compliance**: Ensure all resources have required tags (department, platform, project, environment)
4. **Naming Conventions**: Follow UniSuper naming standards consistently
5. **Documentation**: Include inline comments explaining configuration decisions

### Network Architecture Compliance

#### CAF Landing Zone Principles:
1. **Platform vs Application Separation**: Maintain clear boundaries between platform and application landing zones
2. **Shared Services**: Utilize connectivity hub for shared network services
3. **Security Centralization**: Implement security controls at appropriate layers
4. **Resource Organization**: Organize resources by landing zone and business function
5. **Standardization**: Apply consistent patterns across similar landing zones

#### Hub-and-Spoke Best Practices:
1. **Central Routing**: All traffic flows through connectivity hub
2. **Spoke Isolation**: Each application landing zone has dedicated infrastructure
3. **Security Layering**: Implement NSGs, UDRs, and firewall rules appropriately
4. **DNS Integration**: Centralized private DNS with spoke VNet linking
5. **Private Connectivity**: Use private endpoints for Azure PaaS services

This comprehensive repository guide provides the essential knowledge and operational procedures required for effective management of UniSuper's network infrastructure while maintaining security, performance, and compliance standards within the Azure CAF framework. 