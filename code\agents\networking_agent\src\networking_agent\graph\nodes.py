import logging
from dotenv import load_dotenv
import os
from networking_agent.graph.state import State, Repository
from networking_agent.llms.llm import get_llm
from networking_agent.prompts.template import apply_prompt_template
from langchain.prompts import ChatPromptTemplate
from langchain_mcp_adapters.client import MultiServerMCPClient
from langgraph.types import Command
from langgraph.prebuilt import ToolNode
import json


logger = logging.getLogger(__name__)
load_dotenv()


MCP_URL = os.environ["MCP_URL"]

async def resource_loader_node(state: State):
    logging.info("Entering resources reader node")
    client = MultiServerMCPClient({
        "networking": {
            "url": f"{MCP_URL}/sse",
            "transport": "sse"
        }
    })
    repo_names = await client.get_resources(server_name="networking", uris=[
        "repo://repositories"
    ])
    repo_names = json.loads(repo_names[0].as_string())
    structures = await client.get_resources(server_name="networking", uris=[
        f"repo://{repo_name}/tree" for repo_name in repo_names
    ])
    descriptions = await client.get_resources(server_name="networking", uris=[
        f"repo://{repo_name}/description" for repo_name in repo_names
    ])
    repositories = [Repository(name=repo_name, structure="", description="") for repo_name in repo_names]
    for tree, description, repo in zip(structures, descriptions, repositories):
        repo.structure = tree.as_string()
        repo.description = description.as_string()
    
    logging.info("Completed reading resources")
    return Command(
        update={"repositories": repositories}, 
        goto="llm_with_tools_node"
    )


async def llm_with_tools_node(state: State):
    logging.info("Entering LLM node")
    client = MultiServerMCPClient({
        "networking": {
            "url": f"{MCP_URL}/sse",
            "transport": "sse"
        }
    })
    tools = await client.get_tools()

    llm = get_llm(os.environ["LLM_MODEL"])
    llm_with_tools = llm.bind_tools(tools)

    messages = apply_prompt_template("system_prompt", state)
    prompt_template = ChatPromptTemplate.from_messages(messages)

    chain = prompt_template | llm_with_tools

    structure_section = "\n\n".join([f"### Structure of {repo.name}:\n{repo.structure}" for repo in state["repositories"]])
    description_section = "\n\n".join([f"### Description of {repo.name}:\n{repo.structure}" for repo in state["repositories"]])

    context = {"structure": structure_section, "description": description_section}

    response = chain.invoke(context)

    logging.info("Completed LLM execution")
    if response.tool_calls:
        return Command(update={"messages": response}, goto="tools_node")
    else:
        return Command(update={"messages": response}, goto="__end__")


async def tools_node(state: State):
    logging.info("Entering tool executor node")
    client = MultiServerMCPClient({
        "networking": {
            "url": f"{MCP_URL}/sse",
            "transport": "sse"
        }
    })
    tools = await client.get_tools()

    tools_node = ToolNode(tools)
    response = await tools_node.ainvoke(state)

    logging.info("Completed tool execution")
    return Command(
        update=response,
        goto="llm_with_tools_node"
    )
