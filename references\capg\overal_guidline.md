# UniSuper Azure Network Infrastructure - Comprehensive Architecture Guide v4

## Executive Summary

UniSuper's Azure network infrastructure implements a sophisticated Azure Cloud Adoption Framework (CAF) Hub-and-Spoke topology supporting mission-critical business operations across multiple Azure subscriptions and environments. The infrastructure is segregated into **Production (unisuper.lan)** and **Non-Production (devops.lan)** domains, with distinct Platform and Application landing zones enabling secure, scalable, and compliant network operations.

This comprehensive guide provides essential knowledge for understanding, operating, troubleshooting, and evolving UniSuper's network infrastructure while maintaining the highest standards of security, performance, and operational excellence within the Azure CAF framework.

---

## Strategic Network Architecture Overview

### Azure Cloud Adoption Framework (CAF) Implementation

UniSuper's network architecture follows Azure CAF principles with clear segregation between Platform and Application landing zones:

#### **Platform Landing Zones**
- **Connectivity Landing Zone**: Core network services, ExpressRoute, Azure Firewall, SD-WAN integration
- **Identity Landing Zone**: Identity and access management, Active Directory services  
- **Management Landing Zone**: Monitoring, logging, security services, DevOps tools

#### **Application Landing Zones**
- **Core Administration**: RPA, Power Automate workloads
- **Corporate**: Abacus, finance applications, shared services
- **Digital**: Sitecore, customer portals, member services
- **Data Management**: Data platform, analytics, ERMS, MDP workloads
  - **Environment Types**: dev, tst1, tst2, sit1, sit2, rgr1, rgr2, npd (non-production master)
  - **Specialized Workloads**: Databricks, Analytics Workspaces, Data Services
- **Investments**: Investment management and trading applications
- **Customer Communications**: Customer-facing applications
- **Employer**: Employer-related services
- **Integration**: iPaaS and integration services (legacy USM subscriptions)
- **Messaging**: Messaging and communication services

### Environment Segregation Strategy

#### **Production Domain (unisuper.lan)**
- **Azure Subscriptions**: Multiple subscriptions per landing zone
- **Network Topology**: Hub-and-Spoke with central connectivity hub
- **Security Posture**: Production-grade security controls and monitoring
- **Change Control**: Terraform-managed infrastructure with strict approval workflows

#### **Non-Production Domain (devops.lan)**  
- **Azure Subscriptions**: Shared non-production subscriptions per landing zone
- **Network Topology**: Hub-and-Spoke mirroring production architecture
- **Environment Types**: Multiple granular environments within each application landing zone:
  - **dev**: Development environment
  - **tst1**, **tst2**: Primary and secondary testing environments
  - **sit1**, **sit2**: System Integration Testing environments
  - **rgr1**, **rgr2**: Regression testing environments
  - **npd**: Master non-production environment
- **Change Control**: Terraform-managed with accelerated deployment for testing

### Repository Architecture Implementation

UniSuper manages network infrastructure through three specialized repositories:

#### **1. azure-connectivity-non-production (YAML-Driven Hub-and-Spoke)**
- **Purpose**: Platform landing zone hub-and-spoke topology
- **Configuration Method**: YAML-driven with Terraform module processing
- **Module Source**: Private modules from `app.terraform.io/unisuper/`
- **Coverage**: Central hub infrastructure, spoke peering, shared services

#### **2. azure-usm-non-production-network (Legacy Import Management)**
- **Purpose**: Legacy resource integration using aztfexport methodology
- **Configuration Method**: Import-focused with resource group separation
- **Organization**: Separated by resource type (NSGs/, RouteTables/, Vnets/)
- **Migration Focus**: Gradual transition to CAF landing zones

#### **3. azure-next-gen-firewall-vwan (Virtual WAN Architecture)**
- **Purpose**: Next-generation Virtual WAN with Cloud NGFW
- **Configuration Method**: Direct Terraform HCL (not YAML-driven)
- **Regional Structure**: nonprod-mel/, prod-mel/, prod-syd/
- **Technology**: Palo Alto Cloud NGFW integrated with Virtual WAN

---

## Detailed Network Components

### Hub-and-Spoke Topology Implementation

#### **Connectivity Hub (Platform Landing Zone)**
**Core Components:**
- **Hub VNet**: Central connectivity VNet (vnet-connectivity-prd-mel-001, vnet-connectivity-prd-syd-001)
- **Azure Firewall**: Centralized security inspection for north-south and east-west traffic
- **ExpressRoute Gateways**: Hybrid connectivity to on-premises environments
- **SD-WAN Integration**: Aruba SD-WAN edge devices for branch connectivity
- **VPN Gateways**: Site-to-site and point-to-site VPN connectivity

**Regional Deployment:**
- **Melbourne (australiasoutheast)**: Primary region with full hub infrastructure
- **Sydney (australiaeast)**: Secondary region with redundant hub infrastructure
- **CIDR Allocation**: 
  - Melbourne: *********/21 (Production), **********/21 (Non-Production)
  - Sydney: *********/21 (Production)

#### **Spoke VNets (Application Landing Zones)**
**Production Spoke Examples:**
- **Data Management**: vnet-mdp-prd-mel-01 (**********/21), vnet-mdp-prd-syd-01 (**********/21)
- **Digital**: vnet-digital-prd-mel-01 (***********/21), vnet-digital-prd-syd-01 (***********/21)
- **Corporate**: vnet-abacus-prd-001 (***********/21)
- **Investments**: vnet-inv-prd-syd-01 (***********/23)

**Non-Production Spoke Examples:**
- **Data Management**: vnet-mdp-npd-mel-01 (***********/21, ***********/21)
- **Digital**: vnet-digital-npd-mel-01 (************/21)
- **Corporate**: vnet-finance-non-prod-001 (***********/23)
- **Investments**: vnet-inv-npd-mel-01 (************/23)

### Security Architecture Implementation

#### **Network Security Groups (NSGs)**
**Application Strategy:**
- **Subnet-Level**: Primary NSG deployment on application subnets
- **NIC-Level**: Additional granular controls where required
- **Default Deny**: Explicit allow rules with default deny posture
- **Centralized Management**: Consistent rule templates across landing zones

#### **User-Defined Routes (UDRs)**
**Hub-and-Spoke Routing:**
- **Default Route (0.0.0.0/0)**: Points to Azure Firewall in connectivity hub
- **RFC1918 Routes**: Hub-to-spoke communication via Azure Firewall
- **Internet Egress**: Centralized internet breakout through connectivity hub
- **ExpressRoute Integration**: On-premises connectivity via ExpressRoute gateways

#### **Azure Firewall Configuration**
**Traffic Inspection:**
- **North-South Traffic**: Internet inbound/outbound traffic inspection
- **East-West Traffic**: Inter-spoke communication filtering
- **Application Rules**: Layer 7 filtering and URL categorization
- **Network Rules**: Layer 4 filtering and protocol inspection
- **Threat Intelligence**: Integrated threat detection and prevention

### DNS Architecture

#### **DNS Server Configuration**
**Production DNS Servers:**
- **Melbourne**: 10.1.0.14, 10.1.0.15, *********, *********
- **Sydney**: *********, *********, *********, *********

**Non-Production DNS Servers:**
- **Melbourne**: ************, **********, **********

#### **Private DNS Zones**
**Azure Private DNS Integration:**
- **Service Endpoints**: Limited to managed SQL instances only
- **Private Endpoints**: Primary method for Azure PaaS service connectivity
- **Zone Integration**: Automatic DNS record management for private endpoints
- **Cross-Landing Zone Resolution**: Centralized DNS resolution across all spokes

---

## IP Address Management

### CIDR Allocation Strategy

#### **Production Environment (unisuper.lan)**
**Platform Landing Zones:**
```
Connectivity:
- Melbourne: *********/21, **********/23, **********/24, **********/23
- Sydney: *********/21, **********/23

Identity:
- Melbourne: **********/22
- Sydney: **********/22

Management:
- Melbourne: **********/21
- Sydney: **********/21
```

**Application Landing Zones:**
```
Data Management:
- Melbourne: **********/21, **********/24, **********/27, ***********/23
- Sydney: **********/21, **********/27, ***********/23

Digital:
- Melbourne: ***********/21
- Sydney: ***********/21

Corporate:
- Melbourne: ***********/21

Investments:
- Sydney: ***********/23

Core Administration:
- Sydney: **********/22
```

#### **Non-Production Environment (devops.lan)**
**Platform Landing Zones:**
```
Connectivity: **********/21, ***********/23
Identity: ***********/22
Management: ***********/21
```

**Application Landing Zones:**
```
Data Management: ***********/21, ***********/21, ************/24, ************/27
Digital: ************/21
Corporate: ***********/23
Investments: ************/23
Core Administration: ***********/22
```

### Legacy Network Integration

#### **Legacy Subscriptions**
**USM Production:**
- vnet-inv-syd-01: *********/23, *********/23
- vnet-prd-mel-01: *********/15, *************/22
- vnet-prd-syd-01: *********/15, *************/22
- vnet-shd-mel-01: ********/16, *************/22
- vnet-shd-syd-01: ********/16, *************/22

**USM Non-Production:**
- vnet-npd-mel-01: **********/15

---

## Traffic Flow Patterns

### Hub-and-Spoke Communication Flows

#### **Outbound Internet Traffic (North-South)**
1. **Source**: Application in spoke landing zone VNet
2. **Route Decision**: Default route (0.0.0.0/0) points to Azure Firewall in connectivity hub
3. **Hub Processing**: Azure Firewall inspection and policy enforcement
4. **Internet Egress**: Controlled access based on firewall application and network rules
5. **Return Path**: Stateful firewall maintains connection state for return traffic

#### **Inter-Spoke Communication (East-West)**
1. **Source**: Application in spoke landing zone A
2. **Route Decision**: Specific routes (RFC1918) point to Azure Firewall in connectivity hub
3. **Hub Processing**: Azure Firewall inspection and inter-spoke policy enforcement
4. **Destination Routing**: Traffic routed to target spoke landing zone VNet
5. **Application Delivery**: Final delivery to target application or service

#### **Hybrid Connectivity (On-Premises)**
1. **ExpressRoute Path**: Primary connectivity via ExpressRoute circuits
2. **SD-WAN Integration**: Aruba SD-WAN overlay for branch office connectivity
3. **VPN Backup**: Site-to-site VPN for redundancy and additional connectivity
4. **Route Advertisement**: BGP route exchange for optimal path selection

### Cross-Landing Zone Communication

#### **Application-to-Platform Services**
- **Identity Integration**: Spoke applications connecting to identity services in identity landing zone
- **Management Services**: Logging, monitoring, and DevOps tools connectivity
- **Shared Services**: Cross-landing zone service consumption patterns

#### **Application-to-Application**
- **Data Flow**: Digital applications consuming data management services
- **Integration Patterns**: Corporate applications integrating with other business unit applications
- **Service Mesh**: Controlled application-to-application communication through hub

---

## Operational Procedures and Best Practices

### Change Management Framework

#### **Infrastructure as Code (IaC) Approach**
**Repository Organization:**
- **Platform Landing Zones**: Dedicated repositories per platform component
- **Application Landing Zones**: Individual repositories per business unit/application area
- **Terraform Management**: All infrastructure managed through Terraform with Azure DevOps pipelines
- **Configuration Validation**: Automated testing and validation in non-production environments

#### **Deployment Methodology**
1. **Non-Production Validation**: All changes tested in non-production environments first
2. **Configuration Review**: Peer review of all Terraform configurations
3. **Gradual Rollout**: Staged deployment across landing zones and regions
4. **Rollback Planning**: Automated rollback procedures for failed deployments
5. **Documentation Updates**: Automatic documentation updates from IaC changes

### Security Model Implementation

#### **Defense-in-Depth Strategy**
**Multiple Security Layers:**
- **NSG Level**: Subnet and NIC-level traffic filtering
- **UDR Level**: Routing control and traffic steering
- **Azure Firewall**: Centralized security inspection and policy enforcement
- **Private Endpoints**: Secure connectivity to Azure PaaS services
- **Application Security**: Service-specific security controls within applications

#### **Zero Trust Principles**
- **Explicit Verification**: Every connection verified at multiple points
- **Least Privilege Access**: Minimal required permissions for network communications
- **Assume Breach**: Architecture designed to limit blast radius of potential breaches
- **Continuous Monitoring**: Ongoing analysis of all network traffic and behaviors

### Performance Optimization

#### **Regional Architecture Benefits**
- **Proximity Placement**: Applications deployed in regions closest to users
- **Cross-Region Redundancy**: Melbourne and Sydney regions provide business continuity
- **ExpressRoute Optimization**: Multiple ExpressRoute circuits for optimal performance
- **Azure Backbone**: Leveraging Microsoft's global network infrastructure

#### **Capacity Planning**
- **CIDR Management**: Systematic IP address allocation preventing conflicts
- **Subnet Sizing**: Appropriate subnet sizing for current and future growth
- **Firewall Scaling**: Azure Firewall scaling based on traffic patterns
- **ExpressRoute Bandwidth**: Right-sizing circuits based on utilization patterns

---

## Key Takeaways for Network Operations

### Critical Understanding Points

1. **CAF Landing Zone Architecture**: Platform and Application landing zones with clear separation of concerns
2. **Hub-and-Spoke Topology**: Centralized connectivity and security through connectivity hub
3. **Environment Segregation**: Production (unisuper.lan) and Non-Production (devops.lan) domain separation
4. **Regional Deployment**: Melbourne and Sydney regions with consistent architecture patterns
5. **IaC Management**: All infrastructure managed through Terraform with strict change control
6. **Security Centralization**: Azure Firewall provides centralized security inspection for all traffic flows

### Common Operational Patterns

1. **New Application Onboarding**: Deploy to appropriate application landing zone with standardized networking patterns
2. **Cross-Landing Zone Connectivity**: Use hub-and-spoke routing with proper firewall policy configuration
3. **Security Policy Updates**: Implement through Azure Firewall rules with testing in non-production first
4. **Performance Optimization**: Leverage regional deployment and ExpressRoute connectivity
5. **Troubleshooting Approach**: Systematic analysis through IaC repositories and configuration validation

### Legacy Integration Considerations

- **USM Subscription Migration**: Gradual migration from legacy USM subscriptions to CAF landing zones
- **Network Overlap Management**: Careful CIDR management to prevent conflicts during migration
- **Service Dependencies**: Understanding cross-subscription dependencies during migration phases
- **DNS Integration**: Maintaining DNS resolution during transition periods

This comprehensive understanding of UniSuper's Azure CAF network architecture ensures effective network operations, troubleshooting, and strategic planning for continued infrastructure evolution while maintaining security, performance, and compliance requirements. 