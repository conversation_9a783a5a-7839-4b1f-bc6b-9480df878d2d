# Merlin WebApp
This is the front-end component for the Networking Agent.

Using `Next.js`, the app is built on `Node.js LTS (v22.16.0)`, and the dependencies for this app are listed in `\frontend\package.json`.

### Running
First, please read the 'Merlin Merlin' section of the main `README` in the root of this repository, and ensure you've completed the necessary setup.

The frontend is built on Next.js, and require Node.js to run.

For development:
```bash
cd ./front-end
npm install
npm run dev
```

For deployment, please follow https://nextjs.org/docs/app/getting-started/deploying
```bash
cd ./front-end
npm run build
npm run start
```
