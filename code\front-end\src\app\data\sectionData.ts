import { SectionK<PERSON> } from "../types"

//what does this file do? static data?

export const sectionData: Array<{
  title: SectionK<PERSON>;
  count: number;
  tickets: Array<{
    id: string;
    desc: string;
    messages: Array<{
      id: string;
      role: "user" | "assistant";
      content: string;
      timestamp: Date;
    }>;
  }>;
}> = [
  {
    title: "Awaiting Input", // This must match SectionKey exactly
    count: 2,
    tickets: [
      // your ticket data
    ]
  },
  {
    title: "In Process", // This must match SectionKey exactly
    count: 0,
    tickets: []
  },
  {
    title: "Assigned to", // This must match SectionKey exactly
    count: 0,
    tickets: []
  },
  {
    title: "Archive", // This must match SectionKey exactly
    count: 128,
    tickets: [
      // your ticket data
    ]
  }
];
