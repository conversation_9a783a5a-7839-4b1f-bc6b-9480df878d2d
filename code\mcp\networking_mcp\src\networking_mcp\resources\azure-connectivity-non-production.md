This infrastructure implements a classic **Hub-and-Spoke network topology** in Azure, using Terraform for automation. The design is modular and repeatable, supporting multiple subscriptions, environments, and regions.  
   
#### Key Components  
   
1. **Hub Network ("Connectivity" Subscription)**  
   - Central point for shared services, security, and connectivity.  
   - Hosts core resources: firewalls (Palo Alto VM-Series), load balancers, private DNS zones, ExpressRoute, and Virtual Network Gateway.  
   - All spokes peer with the hub, and most inter-spoke traffic transits the hub (or vWAN in future).  
   
2. **Spoke Networks (per Application/Business Unit)**  
   - Isolated vNets for each major application or business unit.  
   - Each spoke has its own subnets, NSGs, and route tables.  
   - Spokes peer with the hub but not directly with each other.  
   
3. **Peering**  
   - vNet peering is established between the hub and each spoke.  
   - Peering is bidirectional and allows forwarding of traffic, but not gateway transit (by default).  
   
4. **Network Security Groups (NSGs)**  
   - Granular control of traffic at subnet level.  
   - NSG rules are defined per subnet/application, with some defaults and some custom rules.  
   - NSGs are critical for troubleshooting connectivity issues.  
   
5. **Route Tables**  
   - Custom routes direct traffic to firewalls (NVA) or other next hops.  
   - Default route (0.0.0.0/0) in spokes often points to the firewall in the hub.  
   - Route tables are associated with subnets.  
   
6. **Firewalls (NVA)**  
   - Two Palo Alto VM-Series firewalls in the hub for high availability.  
   - Deployed in dedicated subnets with internal and external load balancers.  
   - All outbound and inbound traffic can be routed through these firewalls.  
   
7. **Load Balancers**  
   - Internal LB for distributing outbound flows across firewalls.  
   - External LB for inbound flows (with multiple public IPs for different applications).  
   
8. **Private DNS Zones**  
   - Managed in the hub and linked to vNets for name resolution of private endpoints and services.  
   
---  
   
#### **How Traffic Flows**  
   
#### **Outbound Traffic (from Spoke to Internet or Other Spokes)**  
1. **VM in Spoke** → **Subnet Route Table** (default route points to firewall NVA IP in hub) → **Hub Firewall** → **Internet** or **Other Spoke** (if allowed by NSG/Firewall rules).  
2. **NSG** on the spoke subnet may allow or block the initial traffic.  
3. **Route Table** ensures the packet is sent to the firewall, not directly to Azure default gateway.  
   
#### **Inbound Traffic (from Internet to Application in Spoke)**  
1. **Internet** → **Public IP (External LB)** → **Hub Firewall** → **Internal LB** (if used) → **Spoke Subnet**.  
2. **Firewall** and **NSG** rules must both allow the traffic.  
   
#### **Inter-Spoke Traffic**  
- Routed via the hub (unless direct peering is configured, which is not standard here).  
- **Spoke A** → **Route Table** (default route to firewall) → **Hub Firewall** → **Spoke B**.  
- NSGs on both source and destination subnets, as well as firewall policies, must permit the flow.  
   
---  
   
#### **Troubleshooting Guide**  
   
#### **1. Connectivity Issues Between Applications/Zones**  
- **Check NSGs**: Ensure both source and destination subnets' NSGs allow the required traffic (protocol, port, source/destination).  
- **Check Route Tables**: Confirm that the default route (0.0.0.0/0) in the source subnet points to the correct firewall IP.  
- **Check Peering**: Verify vNet peering is established and configured with `allow_forwarded_traffic = true`.  
- **Check Firewall Policies**: Ensure the Palo Alto firewall allows the specific flow (check logs and policies).  
- **Check UDR Overlaps**: Make sure no conflicting user-defined routes override the desired path.  
   
#### **2. Inbound (Internet) Access to Application**  
- **Public IP**: Is the correct public IP (from the external LB) mapped to the application?  
- **LB Rules**: Is the load balancer rule forwarding the correct port/protocol to the backend (firewall)?  
- **Firewall**: Is the firewall allowing the inbound connection?  
- **NSG**: Is the destination subnet NSG allowing the inbound traffic?  
- **Application**: Is the application listening on the correct port?  
   
#### **3. Outbound Internet Access**  
- **Subnet Route Table**: Is the default route set to the firewall?  
- **Firewall**: Is outbound traffic allowed by policy?  
- **NSG**: Is outbound traffic allowed by subnet NSG?  
- **Azure Policy**: Is there a deny policy at the platform level?  
   
#### **4. Inter-Spoke Traffic**  
- **Route Table**: Both spokes should have default route to firewall.  
- **Firewall**: Policy must allow spoke-to-spoke traffic.  
- **NSGs**: Both source and destination NSGs must allow the flow.  
   
---  
   
#### **Key Azure Resources and Their Relationships**  
   
- **Resource Groups**: Each vNet and its subnets, NSGs, and route tables live in a dedicated RG.  
- **Virtual Networks (vNets)**: Each spoke and the hub have their own vNet, with unique address spaces.  
- **Subnets**: Divided per environment, application, or function (e.g., app, endpoint, control, data).  
- **NSGs**: Attached per subnet, with rules tailored to the application needs.  
- **Route Tables**: Associated per subnet, often with a default route to the firewall.  
- **vNet Peering**: Connects spokes to the hub, with forwarding enabled.  
- **Firewalls (NVA)**: Deployed in the hub, all traffic is routed through them for inspection.  
- **Load Balancers**: Distribute traffic across firewalls and manage inbound flows.  
   
---  
   
#### **Common Patterns in NSGs and Routes**  
   
- **Allow ADO Agents**: Many NSGs allow inbound from Azure DevOps agent subnets for deployment.  
- **Allow Specific App/Infra Flows**: NSGs have rules for SQL, HTTP/S, and other app ports.  
- **Deny All Private Inbound**: Often, a "DenyAnyInboundfromPrivate" rule blocks unwanted lateral movement.  
- **Allow Outbound to Public**: NSGs typically allow outbound to public, but deny to Internet (forcing firewall path).  
- **Default Route to Firewall**: Subnet route tables set 0.0.0.0/0 to the firewall's private IP.  
   
---  
   
#### **Example: Data Management Spoke**  
   
- **vNet**: `vnet-mdp-npd-mel-01` (CIDR: ***********/21, ***********/21)  
- **Subnets**: App, Databricks, Endpoints, etc., each with own NSG and route table.  
- **NSG**: Allows ADO, specific app flows, and denies unwanted traffic.  
- **Route Table**: Default route to `**********` (firewall).  
- **Peering**: Connected to the hub, with forwarding allowed.  
   
---  
   
#### **Summary Table: Troubleshooting Checklist**  
   
| Problem Type        | Check NSG | Check Route Table | Check Peering | Check Firewall | Check LB | Check App |  
|---------------------|----------|------------------|---------------|---------------|----------|----------|  
| Spoke-to-Internet   |    ✔     |        ✔         |      ✔        |      ✔        |    ✔     |    ✔     |  
| Inter-Spoke         |    ✔     |        ✔         |      ✔        |      ✔        |    -     |    ✔     |  
| Inbound (Internet)  |    ✔     |        ✔         |      ✔        |      ✔        |    ✔     |    ✔     |  
| Private Link/DNS    |    -     |        -         |      ✔        |      -        |    -     |    ✔     |  
   
---  
   
#### **Key Takeaways**  
   
- **All traffic (in/out/inter-spoke) is routed via the hub firewall** for security and inspection.  
- **NSGs and route tables are the first line of troubleshooting** for connectivity issues.  
- **Peering must have forwarding enabled** for traffic to flow between hub and spokes.  
- **Firewall rules must be checked if NSG and routes look correct but traffic is still blocked**.  
- **Private DNS zones** in the hub are linked to vNets for private endpoint resolution.  
- **Load balancers** are used for distributing traffic to/from firewalls.  
   
---  
   
#### **If You Get a User Query Like:**  
- "Why can't my app in Spoke A reach the database in Spoke B?"  
  - Check for conflicting NSG rules on both subnets.  
  - Check both subnets' route tables (should point to firewall).  
  - Check peering exists and is healthy.  
  - Check firewall rules for inter-spoke allowance.  
   
- "Why can't my web server be accessed from the Internet?"  
  - Check for conflicting NSG rules.  
  - Check LB public IP and rules.  
  - Check firewall inbound rule.  
  - Check subnet NSG inbound rule.  
  - Check app is listening.  
   