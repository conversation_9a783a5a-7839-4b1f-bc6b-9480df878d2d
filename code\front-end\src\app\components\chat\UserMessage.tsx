import ReactMarkdown from "react-markdown";
import CodeBlock from "./CodeBlock";

interface UserMessageProps {
  content: string;
  timestamp: Date;
}

export default function UserMessage({ content, timestamp }: UserMessageProps) {
  return (
    <div className="flex justify-end mb-4">
      <div className="max-w-[70%]">
        <div className="bg-gray-300 text-black rounded px-4 py-3">
          <div className="text-sm leading-relaxed">
            <ReactMarkdown
              components={{
                span: ({ children }) => (
                  <span className="mb-2 last:mb-0">{children}</span>
                ),
                strong: ({ children }) => (
                  <strong className="font-semibold text-gray-900">
                    {children}
                  </strong>
                ),
                h1: ({ children }) => (
                  <h1 className="text-lg font-bold mb-2 text-gray-900">
                    {children}
                  </h1>
                ),
                h2: ({ children }) => (
                  <h2 className="text-base font-bold mb-2 text-gray-900">
                    {children}
                  </h2>
                ),
                h3: ({ children }) => (
                  <h3 className="text-sm font-bold mb-1 text-gray-900">
                    {children}
                  </h3>
                ),
                ul: ({ children }) => (
                  <ul className="list-disc list-inside mb-2 space-y-1">
                    {children}
                  </ul>
                ),
                ol: ({ children }) => (
                  <ol className="list-decimal list-inside mb-2 space-y-1">
                    {children}
                  </ol>
                ),
                li: ({ children }) => <li className="text-sm">{children}</li>,
                code: (props: any) => {
                  const { inline, className, children, ...rest } = props;
                  return (
                    <CodeBlock className={className} inline={inline} {...rest}>
                      {String(children).replace(/\n$/, "")}
                    </CodeBlock>
                  );
                },
                pre: ({ children }) => <div>{children}</div>,
              }}
            >
              {content}
            </ReactMarkdown>
          </div>
        </div>
      </div>
    </div>
  );
}
