🔹 User Input:
“I added an allow rule "AllowAgenticAI" to access Domain Controller but it still doesn’t work. Any ideas?”

🔹 Explanation & Analysis:
Allow rule for any port exists but has lower priority than deny-all rule.

resource "azurerm_network_security_rule" "AllowAgenticAI" {
  access                      = "Allow"
  destination_address_prefix  = "***********/24"
  destination_port_range      = "*"
  direction                   = "Inbound"
  name                        = "AllowMDPAdoAgent"
  network_security_group_name = "nsg-npd-mel-coreservices"
  priority                    = 1033
  protocol                    = "*"
  resource_group_name         = "rg-npd-network"
  source_address_prefix       = "**********/24"
  source_port_range           = "*"
  depends_on = [
    azurerm_network_security_group.nsg-npd-mel-coreservices,
  ]
}
🔹 Root Cause:
Allow rule is overridden by deny-all due to incorrect priority.

🔹 Terraform Change Required:
Increase priority (i.e., lower number) of allow rule.

🔹 File to Edit:
azure-usm-non-production-network/NSGs/rg-npd-network/main.aztfexport.tf

🔹 Updated File:
hcl
Copy
Edit
resource "azurerm_network_security_rule" "AllowAgenticAI" {
  access                      = "Allow"
  destination_address_prefix  = "***********/24"
  destination_port_range      = "*"
  direction                   = "Inbound"
  name                        = "AllowMDPAdoAgent"
  network_security_group_name = "nsg-npd-mel-coreservices"
  priority                    = 1032 
  protocol                    = "*"
  resource_group_name         = "rg-npd-network"
  source_address_prefix       = "**********/24"
  source_port_range           = "*"
  depends_on = [
    azurerm_network_security_group.nsg-npd-mel-coreservices,
  ]
}
resource "azurerm_network_security_rule" "nsg-npd-mel-coreservices_AZ_Deny_Inbound_Any_Any_Any_Any" {
  access                      = "Deny"
  destination_address_prefix  = "*"
  destination_port_range      = "*"
  direction                   = "Inbound"
  name                        = "AZ_Deny_Inbound_Any_Any_Any_Any"
  network_security_group_name = "nsg-npd-mel-coreservices"
  priority                    = 1050 # increase the number to lower the priority
  protocol                    = "*"
  resource_group_name         = "rg-npd-network"
  source_address_prefix       = "*"
  source_port_range           = "*"
  depends_on = [
    azurerm_network_security_group.nsg-npd-mel-coreservices,
  ]
}