import React, { useState } from "react";
import { Highlight, themes } from "prism-react-renderer";
import Icon from "../../icons/Icon";

interface CodeBlockProps {
  children: string;
  className?: string;
  isBlock?: boolean;
  [key: string]: any;
}

export default function CodeBlock({ children, className, isBlock = true, ...props }: CodeBlockProps) {
  const [copied, setCopied] = useState(false);
  
  // Extract language from className (format: "language-python")
  const match = /language-(\w+)/.exec(className || "");
  const language = match ? match[1] : "text";
  
  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(children);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error("Failed to copy text: ", err);
    }
  };

  // For inline code, use simple styling with better text wrapping
  if (!isBlock) {
    return (
      <code 
        className="bg-gray-100 px-1.5 py-0.5 rounded text-xs font-mono break-words whitespace-pre-wrap" 
        {...props}
      >
        {children}
      </code>
    );
  }

  // For code blocks, use Prism highlighting with responsive width
  return (
    <div className="my-4 w-full max-w-[90%] md:max-w-[80%] lg:max-w-[70%]">
      {/* Language label and copy button */}
      <div className="flex items-center justify-between bg-black text-white px-4 py-2 text-xs font-medium rounded-t">
        <span className="text-gray-300">
          {language.charAt(0).toUpperCase() + language.slice(1)}
        </span>
        <button
          onClick={handleCopy}
          className="flex items-center space-x-1 px-2 py-1 hover:bg-gray-800 rounded transition-colors"
          title={copied ? "Copied!" : "Copy code"}
          aria-label={copied ? "Code copied to clipboard" : "Copy code to clipboard"}
        >
          {copied ? (
            <Icon name="check" className="w-5 h-5 text-green-400" />
          ) : (
            <Icon name="copy" className="w-5 h-5 text-gray-300 hover:text-white" />
          )}
        </button>
      </div>
      
      {/* Code content with syntax highlighting */}
      <div className="rounded-b-lg overflow-hidden">
        <Highlight
          theme={themes.vsDark}
          code={children.trim()}
          language={language as any}
        >
          {({ className: prismClassName, style, tokens, getLineProps, getTokenProps }) => (
            <pre 
              className={`${prismClassName} p-4 m-0 overflow-auto text-sm leading-relaxed font-mono`}
              style={{
                ...style,
                maxHeight: '500px', // Prevent extremely long code blocks from taking too much space
              }}
            >
              {tokens.map((line, i) => (
                <div key={i} {...getLineProps({ line })}>
                  {line.map((token, key) => (
                    <span key={key} {...getTokenProps({ token })} />
                  ))}
                </div>
              ))}
            </pre>
          )}
        </Highlight>
      </div>
    </div>
  );
}
