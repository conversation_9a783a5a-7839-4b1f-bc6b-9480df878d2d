🔹 User Input:
“I have new landing zone and vnet setup (vnet-digital-npd-mel-01), but I couldnt get my VMs on this landing zone to talk to others.”

🔹 Explanation & Analysis:
New vnet will need to peer with virtual wan hub to establish routing, then only firewall NSG blocking.
First check if peering of new vnet to hub is established, then check firewall for specific IP blocking.

🔹 Root Cause:
There is no routing established due to missing peering for new vnet

🔹 Terraform Change Required:
Add new vnet peering to virtual wan hub

🔹 File to Edit:
azure-next-gen-firewall-vwan/nonprod-mel/spoke-peerings.tf

🔹 Updated File:
hcl
Copy
Edit
# VNET:  vnet-digital-npd-mel-01
resource "azurerm_virtual_hub_connection" "npd-digital" {
  name                      = "vhc-npd-digital"
  virtual_hub_id            = azurerm_virtual_hub.vwan_hub.id
  remote_virtual_network_id = "/subscriptions/f1728a6a-676f-4644-82c1-71e4fa59b0d7/resourceGroups/rg-digital-network-npd-mel-01/providers/Microsoft.Network/virtualNetworks/vnet-digital-npd-mel-01"
}
