module "sa_blob_private_endpoint" {
  source  = "app.terraform.io/unisuper/private-endpoint/azurerm"
  version = "2.0.0"
  providers = {
    azurerm     = azurerm
    azurerm.dns = azurerm.dns # this is required for the private DNS zone lookup
  }

  name                    = "pe-${module.naming.storage_account_name}-blob"
  location                = module.resource_group.location
  resource_group_name     = module.resource_group.name
  subnet_id               = data.azurerm_subnet.pe_snet.id
  target_resource         = azurerm_storage_account.default.id
  subresource_name        = "blob"
  environment_tag         = var.environment #"non-production" # only takes "non-production" or "production"
  private_dns_zones_names = ["privatelink.blob.core.windows.net"]

  tags = local.common_tags

  depends_on = [azurerm_storage_account.default]
}
module "sa_file_private_endpoint" {
  source  = "app.terraform.io/unisuper/private-endpoint/azurerm"
  version = "2.0.0"
  providers = {
    azurerm     = azurerm
    azurerm.dns = azurerm.dns # this is required for the private DNS zone lookup
  }

  name                    = "pe-${module.naming.storage_account_name}"
  location                = module.resource_group.location
  resource_group_name     = module.resource_group.name
  subnet_id               = data.azurerm_subnet.pe_snet.id
  target_resource         = azurerm_storage_account.default.id
  subresource_name        = "file"
  environment_tag         = var.environment #"non-production" # only takes "non-production" or "production"
  private_dns_zones_names = ["privatelink.file.core.windows.net"]

  tags = local.common_tags

  depends_on = [azurerm_storage_account.default]
}
module "kv_private_endpoint" {
  source  = "app.terraform.io/unisuper/private-endpoint/azurerm"
  version = "2.0.0"
  providers = {
    azurerm     = azurerm
    azurerm.dns = azurerm.dns # this is required for the private DNS zone lookup
  }

  name                    = "pe-${module.naming.key_vault_name}"
  location                = module.resource_group.location
  resource_group_name     = module.resource_group.name
  subnet_id               = data.azurerm_subnet.pe_snet.id
  target_resource         = azurerm_key_vault.default.id
  subresource_name        = "vault"
  environment_tag         = var.environment #"non-production" # only takes "non-production" or "production"
  private_dns_zones_names = ["privatelink.vaultcore.azure.net"]

  tags = local.common_tags

  depends_on = [azurerm_key_vault.default]
}
module "aiservice_private_endpoint" {
  source  = "app.terraform.io/unisuper/private-endpoint/azurerm"
  version = "2.0.0"
  providers = {
    azurerm     = azurerm
    azurerm.dns = azurerm.dns # this is required for the private DNS zone lookup
  }

  name                    = "pe-${azapi_resource.AIServicesResource.name}"
  location                = module.resource_group.location
  resource_group_name     = module.resource_group.name
  subnet_id               = data.azurerm_subnet.pe_snet.id
  target_resource         = azapi_resource.AIServicesResource.id
  subresource_name        = "account"
  environment_tag         = var.environment #"non-production" # only takes "non-production" or "production"
  private_dns_zones_names = ["privatelink.cognitiveservices.azure.com", "privatelink.openai.azure.com", "privatelink.services.ai.azure.com"]

  tags = local.common_tags

  depends_on = [azapi_resource.AIServicesResource]
}
module "aihub_private_endpoint" {
  source  = "app.terraform.io/unisuper/private-endpoint/azurerm"
  version = "2.0.0"
  providers = {
    azurerm     = azurerm
    azurerm.dns = azurerm.dns # this is required for the private DNS zone lookup
  }

  name                    = "pe-${azapi_resource.hub.name}"
  location                = module.resource_group.location
  resource_group_name     = module.resource_group.name
  subnet_id               = data.azurerm_subnet.pe_snet.id
  target_resource         = azapi_resource.hub.id
  subresource_name        = "amlworkspace"
  environment_tag         = var.environment #"non-production" # only takes "non-production" or "production"
  private_dns_zones_names = ["privatelink.api.azureml.ms", "privatelink.notebooks.azure.net"]

  tags = local.common_tags

  depends_on = [azapi_resource.hub]
}
module "aisearch_private_endpoint" {
  source  = "app.terraform.io/unisuper/private-endpoint/azurerm"
  version = "2.0.0"
  providers = {
    azurerm     = azurerm
    azurerm.dns = azurerm.dns # this is required for the private DNS zone lookup
  }

  name                    = "pe-${module.naming.search_service_name}"
  location                = module.resource_group.location
  resource_group_name     = module.resource_group.name
  subnet_id               = data.azurerm_subnet.pe_snet.id
  target_resource         = azurerm_search_service.ai_search.id
  subresource_name        = "searchService"
  environment_tag         = var.environment #"non-production" # only takes "non-production" or "production"
  private_dns_zones_names = ["privatelink.search.windows.net"]

  tags = local.common_tags

  depends_on = [azurerm_search_service.ai_search]
}


module "a2a_app_private_endpoint" {
  source  = "app.terraform.io/unisuper/private-endpoint/azurerm"
  version = "2.0.0"
  providers = {
    azurerm     = azurerm
    azurerm.dns = azurerm.dns # this is required for the private DNS zone lookup
  }

  name                    = "pe-${module.naming.web_app_name}-a2a"
  location                = module.resource_group.location
  resource_group_name     = module.resource_group.name
  subnet_id               = data.azurerm_subnet.pe_subnet2.id
  target_resource         = azurerm_linux_web_app.a2a.id
  subresource_name        = "sites"
  environment_tag         = var.environment #"non-production" # only takes "non-production" or "production"
  private_dns_zones_names = ["privatelink.azurewebsites.net"]

  tags = local.common_tags

  depends_on = [azurerm_linux_web_app.a2a]
}


module "frontend_app_private_endpoint" {
  source  = "app.terraform.io/unisuper/private-endpoint/azurerm"
  version = "2.0.0"
  providers = {
    azurerm     = azurerm
    azurerm.dns = azurerm.dns # this is required for the private DNS zone lookup
  }

  name                    = "pe-${module.naming.web_app_name}-frontend"
  location                = module.resource_group.location
  resource_group_name     = module.resource_group.name
  subnet_id               = data.azurerm_subnet.pe_subnet2.id
  target_resource         = azurerm_linux_web_app.nwagt.id
  subresource_name        = "sites"
  environment_tag         = var.environment #"non-production" # only takes "non-production" or "production"
  private_dns_zones_names = ["privatelink.azurewebsites.net"]

  tags = local.common_tags

  depends_on = [azurerm_linux_web_app.nwagt]
}


module "mcp_app_private_endpoint" {
  source  = "app.terraform.io/unisuper/private-endpoint/azurerm"
  version = "2.0.0"
  providers = {
    azurerm     = azurerm
    azurerm.dns = azurerm.dns # this is required for the private DNS zone lookup
  }

  name                    = "pe-${module.naming.web_app_name}-mcp"
  location                = module.resource_group.location
  resource_group_name     = module.resource_group.name
  subnet_id               = data.azurerm_subnet.pe_subnet2.id
  target_resource         = azurerm_linux_web_app.mcp.id
  subresource_name        = "sites"
  environment_tag         = var.environment #"non-production" # only takes "non-production" or "production"
  private_dns_zones_names = ["privatelink.azurewebsites.net"]

  tags = local.common_tags

  depends_on = [azurerm_linux_web_app.mcp]
}


module "openai_private_endpoint" {
  source  = "app.terraform.io/unisuper/private-endpoint/azurerm"
  version = "2.0.0"
  providers = {
    azurerm     = azurerm
    azurerm.dns = azurerm.dns # this is required for the private DNS zone lookup
  }

  name                    = "pe-demoopenaiagent"
  location                = module.resource_group.location
  resource_group_name     = module.resource_group.name
  subnet_id               = data.azurerm_subnet.pe_subnet2.id
  target_resource         = azurerm_cognitive_account.openai.id
  subresource_name        = "account"
  environment_tag         = var.environment #"non-production" # only takes "non-production" or "production"
  private_dns_zones_names = ["privatelink.openai.azure.com"]

  tags = local.common_tags

  depends_on = [azurerm_cognitive_account.openai]
}

module "cosmosdb_private_endpoint" {
  source  = "app.terraform.io/unisuper/private-endpoint/azurerm"
  version = "2.0.2"
  providers = {
    azurerm     = azurerm
    azurerm.dns = azurerm.dns # this is required for the private DNS zone lookup
  }

  name                    = "pe-${module.naming.cosmos_db_database_name}"
  location                = module.resource_group.location
  resource_group_name     = module.resource_group.name
  subnet_id               = data.azurerm_subnet.pe_subnet2.id
  target_resource         = azurerm_cosmosdb_postgresql_cluster.cosmosdb.id
  subresource_name        = "coordinator"
  environment_tag         = var.environment #"non-production" # only takes "non-production" or "production"
  private_dns_zones_names = ["privatelink.postgres.cosmos.azure.com"]

  tags = local.common_tags

  depends_on = [azurerm_cosmosdb_postgresql_cluster.cosmosdb]
}