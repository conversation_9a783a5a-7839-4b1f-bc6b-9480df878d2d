"use client";

import TicketSection from "./TicketSection";
import Icon from "../icons/Icon";
import { ticketSections } from "../data/ticketData";
import { TicketData, UIMessage } from "../types";

interface SidebarContentProps {
  activeTab: "tickets" | "chat";
  openSections: { [key: string]: boolean };
  onSectionToggle: (title: string) => void;
  onTicketSelect?: (ticket: TicketData) => void;
  conversations?: Array<{
    id: string;
    name: string;
    time: string;
    lastMessage: string;
    fullHistory: UIMessage[];
  }>;
  loadingConversations?: boolean;
}

export default function SidebarContent({
  activeTab,
  openSections,
  onSectionToggle,
  onTicketSelect,
  conversations = [],
  loadingConversations = false,
}: SidebarContentProps) {
  return (
    <div className="flex-1 overflow-hidden">
      {/* Ticket Sections */}
      {activeTab === "tickets" && (
        <div className="h-full overflow-y-auto px-2 mt-2 pb-4">
          {ticketSections.map((section) => (
            <TicketSection
              key={section.title}
              title={section.title}
              count={section.count}
              tickets={section.tickets}
              isOpen={openSections[section.title]}
              onToggle={onSectionToggle}
              onTicketSelect={onTicketSelect}
            />
          ))}
        </div>
      )}

      {/* Chat content if active */}
      {activeTab === "chat" && (
        <div className="h-full overflow-y-auto px-2 mt-2 pb-4">
          {loadingConversations ? (
            <div className="flex items-center justify-center h-full">
              <div className="text-center p-4 text-gray-400">
                Loading conversations...
              </div>
            </div>
          ) : conversations.length > 0 ? (
            conversations.map((chat, idx) => (
              <div
                key={chat.id ? `${chat.id}-${idx}` : idx}
                className="p-2.5 my-0.5 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors"
                onClick={() => {
                  if (onTicketSelect) {
                    onTicketSelect({
                      id: chat.id,
                      desc: chat.name,
                      messages: chat.fullHistory,
                    });
                  }
                }}
              >
                <div className="flex justify-between items-start mb-0.5">
                  <span className="font-medium text-xs text-gray-800">
                    {(() => {
                      let displayName = chat.name;
                      try {
                        // Try to parse chat.name as JSON and extract 'query' if present
                        const parsed = JSON.parse(chat.name);
                        if (parsed && typeof parsed === "object" && parsed.query) {
                          displayName = parsed.query;
                        }
                      } catch (e) {
                        // If parsing fails, fallback to original chat.name
                      }
                      return displayName.charAt(0).toUpperCase() + displayName.slice(1);
                    })()}
                  </span>

                  <span className="text-[10px] text-gray-500">{chat.time}</span>
                </div>
                <p className="text-[10px] text-gray-400 truncate leading-tight">
                  {chat.lastMessage}
                </p>
              </div>
            ))
          ) : (
            <div className="flex items-center justify-center h-full">
              <div className="text-center p-4">
                <div className="w-12 h-12 rounded-full bg-gray-100 flex items-center justify-center mx-auto mb-3">
                  <Icon name="chat" className="h-6 w-6 text-gray-400" />
                </div>
                <p className="text-gray-500 text-sm">No recent chats</p>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
