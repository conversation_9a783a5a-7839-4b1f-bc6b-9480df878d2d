# Advanced Network Troubleshooting Guide v4 - UniSuper Azure Infrastructure

## Overview

This comprehensive troubleshooting guide provides systematic methodologies, diagnostic procedures, and resolution strategies for complex network issues across UniSuper's **Azure Cloud Adoption Framework (CAF) Hub-and-Spoke infrastructure**. The guide focuses on repository-based troubleshooting through Infrastructure-as-Code (IaC) analysis, configuration validation, and systematic problem resolution using terraform and YAML configurations.

Key architectural components that impact troubleshooting:
- **Azure CAF Landing Zones**: Platform (Connectivity, Identity, Management) and Application (Core Administration, Corporate, Digital, Data Management, Investments, etc.)
- **Environment Segregation**: Production (unisuper.lan) and Non-Production (devops.lan) domains
- **Repository-Based Infrastructure**: Three specialized repositories managing different network architectures
- **Multi-Environment Support**: Granular environment types including dev, tst1, tst2, sit1, sit2, rgr1, rgr2, and npd

### Repository-First Troubleshooting Philosophy

All network troubleshooting at UniSuper follows a **repository-first methodology** where configuration analysis, problem identification, and solution implementation are driven through Infrastructure-as-Code (IaC) repositories rather than direct Azure console access or command-line tools.

**Core Principle**: Every network configuration, routing decision, security rule, and connectivity pattern is defined in terraform or YAML files within source-controlled repositories. Therefore, all troubleshooting must begin with repository analysis.

---

## Repository Architecture for Troubleshooting

### Three-Repository Network Strategy

#### **1. azure-connectivity-non-production (YAML-Driven Hub-and-Spoke)**
- **Troubleshooting Focus**: Hub infrastructure, spoke peering, shared services, DNS resolution
- **Configuration Method**: YAML-driven with Terraform modules from `app.terraform.io/unisuper/`
- **Key Components**: VNets, NSGs, route tables, ExpressRoute, Azure Firewall

#### **2. azure-usm-non-production-network (Legacy Import Management)**
- **Troubleshooting Focus**: Legacy resource integration, aztfexport compatibility, migration planning
- **Configuration Method**: Import-based approach with resource group separation
- **Key Components**: Imported NSGs, route tables, VNets separated by resource groups

#### **3. azure-next-gen-firewall-vwan (Virtual WAN Architecture)**
- **Troubleshooting Focus**: Virtual WAN connectivity, Cloud NGFW policies, regional architecture
- **Configuration Method**: Direct Terraform HCL configurations
- **Key Components**: Virtual WAN hubs, spoke connections, Palo Alto Cloud NGFW

### Environment-Specific Troubleshooting Considerations

#### **Data Management Landing Zone Environments**
Based on actual repository configurations, the Data Management landing zone supports eight distinct environments requiring specialized troubleshooting approaches:

- **dev**: Development environment (172.28.80.x/26 subnets)
- **tst1**: Primary testing environment (172.28.81.x/26 subnets)
- **tst2**: Secondary testing environment (172.28.82.x/26 subnets)
- **sit1**: System Integration Testing 1 (172.28.83.x/26 subnets)
- **sit2**: System Integration Testing 2 (172.28.84.x/26 subnets)
- **rgr1**: Regression testing 1 (172.28.85.x/26 subnets)
- **rgr2**: Regression testing 2 (172.28.86.x/26 subnets)
- **npd**: Non-production master (172.28.87.x/26 subnets)

**Environment-Specific Troubleshooting Pattern**: Each environment has dedicated NSGs, subnets, and potentially different routing configurations. When troubleshooting, always verify the specific environment suffix in resource names and subnet allocations.

---

## Systematic Troubleshooting Methodology

### Repository-Based Diagnostic Framework

#### **Step 1: Domain Identification**
**Determine Affected Environment:**
- **Production Domain (unisuper.lan)**: Critical business operations, strict change control
- **Non-Production Domain (devops.lan)**: Development, Test, SIT, Regression environments
- **Legacy Domains**: USM Production and USM Non-Production subscriptions

**Repository Mapping:**
```
Production:
├── Connectivity Production (Platform)
├── Identity Production (Platform)
├── Management Production (Platform)
├── Data Management Production (Application)
├── Digital Production (Application)
├── Corporate Production (Application)
└── Investments Production (Application)

Non-Production:
├── Connectivity Non-Production (Platform)
├── Identity Non-Production (Platform)
├── Management Non-Production (Platform)
├── Data Management Non-Production (Application)
├── Digital Non-Production (Application)
├── Corporate Non-Production (Application)
└── Investments Non-Production (Application)
```

#### **Step 2: Platform Component Analysis**
**Hub-and-Spoke Component Identification:**
- **Connectivity Hub**: Central VNet with Azure Firewall, ExpressRoute, VPN gateways
- **Spoke VNets**: Application landing zone VNets with specific CIDR allocations
- **VNet Peering**: Hub-to-spoke connectivity configurations
- **Route Tables**: User-Defined Routes (UDRs) for traffic steering
- **NSG Configurations**: Network Security Groups for traffic filtering

#### **Step 3: IaC Configuration Analysis**
**Repository Structure Examination:**
1. **Terraform Configuration Files**: Review .tf files for resource definitions
2. **YAML Configuration Files**: Analyze YAML-driven configurations where applicable
3. **Variable Files**: Check terraform.tfvars and variable definitions
4. **Module References**: Understand module dependencies and inheritance
5. **Pipeline Configurations**: Review Azure DevOps pipeline definitions

---

## Critical Issue Categories and Resolution Strategies

### Hub-and-Spoke Connectivity Issues

#### **Scenario 1: Inter-Spoke Communication Failures**
**Common Symptoms:**
- Applications in one spoke cannot reach services in another spoke
- Database connectivity failures between different landing zones
- Cross-spoke API calls timing out or failing

**Repository-Based Diagnostic Approach:**
1. **VNet Peering Configuration Review:**
   ```terraform
   # Check peering configuration in connectivity repository
   resource "azurerm_virtual_network_peering" "hub_to_spoke" {
     name                      = "hub-to-[spoke-name]"
     resource_group_name       = var.hub_resource_group
     virtual_network_name      = var.hub_vnet_name
     remote_virtual_network_id = var.spoke_vnet_id
     allow_virtual_network_access = true
     allow_forwarded_traffic      = true
     allow_gateway_transit        = true
   }
   ```

2. **Route Table Configuration Analysis:**
   ```terraform
   # Verify UDR configuration pointing to Azure Firewall
   resource "azurerm_route_table" "spoke_routes" {
     name                = "rt-[spoke-name]"
     route {
       name           = "InterSpokeTraffic"
       address_prefix = "10.0.0.0/8"
       next_hop_type  = "VirtualAppliance"
       next_hop_in_ip_address = var.azure_firewall_ip
     }
   }
   ```

3. **NSG Rule Validation:**
   ```terraform
   # Check NSG rules for required traffic flows
   resource "azurerm_network_security_rule" "allow_inter_spoke" {
     name                       = "AllowInterSpokeTraffic"
     priority                   = 100
     direction                  = "Inbound"
     access                     = "Allow"
     protocol                   = "Tcp"
     source_port_range          = "*"
     destination_port_range     = "443"
     source_address_prefix      = "*********/16"
     destination_address_prefix = "*********/16"
   }
   ```

**Resolution Strategy Through IaC:**
1. **Peering Fixes**: Update peering configurations to enable required traffic flows
2. **Route Corrections**: Modify UDR configurations to ensure proper next-hop settings
3. **NSG Updates**: Add necessary allow rules for inter-spoke communication
4. **Firewall Rules**: Update Azure Firewall network rules for spoke-to-spoke traffic

#### **Scenario 2: Internet Connectivity Failures**
**Common Symptoms:**
- Applications cannot reach external APIs or websites
- Outbound internet connections timeout or are blocked
- DNS resolution failures for external domains

**Repository Analysis Approach:**
1. **Default Route Configuration:**
   ```terraform
   # Verify default route points to Azure Firewall
   resource "azurerm_route" "default_route" {
     name               = "DefaultRoute"
     resource_group_name = var.resource_group_name
     route_table_name   = var.route_table_name
     address_prefix     = "0.0.0.0/0"
     next_hop_type      = "VirtualAppliance"
     next_hop_in_ip_address = var.azure_firewall_private_ip
   }
   ```

2. **Azure Firewall Rule Analysis:**
   ```terraform
   # Check firewall application rules for internet access
   resource "azurerm_firewall_application_rule_collection" "internet_access" {
     name               = "AllowInternetAccess"
     azure_firewall_name = var.firewall_name
     resource_group_name = var.resource_group_name
     priority           = 100
     action             = "Allow"
     
     rule {
       name = "AllowHTTPS"
       source_addresses = ["*********/16", "*********/16"]
       target_fqdns     = ["*.microsoft.com", "*.azure.com"]
       protocol {
         port = "443"
         type = "Https"
       }
     }
   }
   ```

3. **DNS Configuration Validation:**
   ```terraform
   # Verify DNS server configuration in VNet
   resource "azurerm_virtual_network" "spoke_vnet" {
     name                = var.vnet_name
     location            = var.location
     resource_group_name = var.resource_group_name
     address_space       = [var.vnet_cidr]
     
     dns_servers = ["*********", "*********", "*********", "*********"]
   }
   ```

### ExpressRoute/VPN Connectivity Issues

#### **Scenario 3: ExpressRoute Circuit Failures**
**Common Symptoms:**
- On-premises to Azure connectivity failures
- BGP peering status showing as down
- Route advertisement not working properly

**IaC Configuration Review:**
1. **ExpressRoute Gateway Configuration:**
   ```terraform
   resource "azurerm_virtual_network_gateway" "expressroute_gateway" {
     name                = "vgw-expressroute-[region]"
     location            = var.location
     resource_group_name = var.resource_group_name
     type                = "ExpressRoute"
     vpn_type            = "RouteBased"
     
     sku                 = "Standard"
     enable_bgp          = true
     
     ip_configuration {
       public_ip_address_id          = azurerm_public_ip.gateway_pip.id
       private_ip_address_allocation = "Dynamic"
       subnet_id                     = azurerm_subnet.gateway_subnet.id
     }
   }
   ```

2. **ExpressRoute Connection Analysis:**
   ```terraform
   resource "azurerm_virtual_network_gateway_connection" "expressroute_connection" {
     name                = "conn-expressroute-[circuit-name]"
     location            = var.location
     resource_group_name = var.resource_group_name
     type                = "ExpressRoute"
     virtual_network_gateway_id = azurerm_virtual_network_gateway.expressroute_gateway.id
     express_route_circuit_id   = var.express_route_circuit_id
   }
   ```

#### **Scenario 4: VPN Connectivity Issues**
**Repository Configuration Analysis:**
1. **VPN Gateway Settings:**
   ```terraform
   resource "azurerm_virtual_network_gateway" "vpn_gateway" {
     name                = "vgw-vpn-[region]"
     location            = var.location
     resource_group_name = var.resource_group_name
     type                = "Vpn"
     vpn_type            = "RouteBased"
     
     sku                 = "VpnGw1"
     
     ip_configuration {
       public_ip_address_id          = azurerm_public_ip.vpn_gateway_pip.id
       private_ip_address_allocation = "Dynamic"
       subnet_id                     = azurerm_subnet.gateway_subnet.id
     }
   }
   ```

2. **Local Network Gateway Configuration:**
   ```terraform
   resource "azurerm_local_network_gateway" "onprem_gateway" {
     name                = "lng-onprem-[site]"
     location            = var.location
     resource_group_name = var.resource_group_name
     gateway_address     = var.onprem_gateway_ip
     address_space       = var.onprem_address_spaces
   }
   ```

### DNS Resolution Issues

#### **Scenario 5: Private DNS Zone Integration Failures**
**Common Symptoms:**
- Cannot resolve Azure PaaS service private endpoints
- DNS queries return public IPs instead of private IPs
- Application cannot connect to Azure services via private endpoints

**DNS Configuration Analysis:**
1. **Private DNS Zone Configuration:**
   ```terraform
   resource "azurerm_private_dns_zone" "sql_zone" {
     name                = "privatelink.database.windows.net"
     resource_group_name = var.resource_group_name
   }
   
   resource "azurerm_private_dns_zone_virtual_network_link" "sql_zone_link" {
     name                  = "link-sql-[vnet-name]"
     resource_group_name   = var.resource_group_name
     private_dns_zone_name = azurerm_private_dns_zone.sql_zone.name
     virtual_network_id    = var.vnet_id
   }
   ```

2. **Private Endpoint DNS Integration:**
   ```terraform
   resource "azurerm_private_endpoint" "sql_endpoint" {
     name                = "pe-sql-[service-name]"
     location            = var.location
     resource_group_name = var.resource_group_name
     subnet_id           = var.subnet_id
     
     private_service_connection {
       name                           = "psc-sql-[service-name]"
       private_connection_resource_id = var.sql_server_id
       subresource_names              = ["sqlServer"]
       is_manual_connection           = false
     }
     
     private_dns_zone_group {
       name                 = "default"
       private_dns_zone_ids = [azurerm_private_dns_zone.sql_zone.id]
     }
   }
   ```

### Service Endpoint vs Private Endpoint Configuration Issues

#### **Scenario 6: Incorrect Service Endpoint Usage**
**Common Symptoms:**
- Unexpected routing behavior for Azure PaaS services
- Performance issues with Azure service connectivity
- Security policy violations for PaaS access

**Configuration Compliance Review:**
1. **Service Endpoint Configuration Analysis:**
   ```terraform
   # Service endpoints should ONLY be used for managed SQL
   resource "azurerm_subnet" "app_subnet" {
     name                 = "snet-[application]-[environment]"
     resource_group_name  = var.resource_group_name
     virtual_network_name = var.vnet_name
     address_prefixes     = [var.subnet_cidr]
     
     # Only for managed SQL instances
     service_endpoints = ["Microsoft.Sql"]
   }
   ```

2. **Private Endpoint Implementation for Other Services:**
   ```terraform
   # Private endpoints for all other PaaS services
   resource "azurerm_private_endpoint" "storage_endpoint" {
     name                = "pe-storage-[service-name]"
     location            = var.location
     resource_group_name = var.resource_group_name
     subnet_id           = var.private_endpoint_subnet_id
     
     private_service_connection {
       name                           = "psc-storage-[service-name]"
       private_connection_resource_id = var.storage_account_id
       subresource_names              = ["blob"]
       is_manual_connection           = false
     }
   }
   ```

---

## Performance Troubleshooting and Optimization

### Repository-Based Performance Analysis

#### **Azure Firewall Performance Issues**
**Configuration Optimization:**
1. **Firewall SKU Analysis:**
   ```terraform
   resource "azurerm_firewall" "hub_firewall" {
     name                = "afw-hub-[region]"
     location            = var.location
     resource_group_name = var.resource_group_name
     sku_name            = "AZFW_VNet"
     sku_tier            = "Premium"  # For high-performance requirements
     
     ip_configuration {
       name                 = "configuration"
       subnet_id            = var.firewall_subnet_id
       public_ip_address_id = var.firewall_public_ip_id
     }
   }
   ```

2. **Rule Optimization:**
   ```terraform
   # Optimize rule ordering for performance
   resource "azurerm_firewall_network_rule_collection" "high_priority_rules" {
     name               = "HighPriorityRules"
     azure_firewall_name = azurerm_firewall.hub_firewall.name
     resource_group_name = var.resource_group_name
     priority           = 100  # Higher priority for frequently used rules
     action             = "Allow"
     
     rule {
       name                  = "AllowCriticalTraffic"
       source_addresses      = ["**********/21"]  # Data Management
       destination_addresses = ["***********/21"] # Digital
       destination_ports     = ["443", "1433"]
       protocols             = ["TCP"]
     }
   }
   ```

#### **VNet Peering Performance Optimization**
**Configuration Analysis:**
```terraform
# Enable optimized peering settings
resource "azurerm_virtual_network_peering" "optimized_peering" {
  name                      = "peer-hub-to-spoke"
  resource_group_name       = var.hub_resource_group
  virtual_network_name      = var.hub_vnet_name
  remote_virtual_network_id = var.spoke_vnet_id
  
  allow_virtual_network_access = true
  allow_forwarded_traffic      = true
  allow_gateway_transit        = true
  use_remote_gateways          = false
}
```

---

## Preventive Measures and Best Practices

### Repository-Based Monitoring and Validation

#### **Configuration Validation Framework**
**IaC Validation Patterns:**
1. **Terraform Plan Analysis**: Review terraform plans for unexpected changes
2. **Configuration Drift Detection**: Compare deployed resources with IaC definitions
3. **Security Policy Compliance**: Validate NSG rules and firewall policies
4. **CIDR Conflict Detection**: Prevent overlapping IP address allocations
5. **Resource Dependency Mapping**: Understand cross-repository dependencies

#### **Automated Testing Integration**
**Pipeline Validation:**
```yaml
# Azure DevOps pipeline validation
trigger:
  branches:
    include:
    - main
    - develop

stages:
- stage: Validate
  jobs:
  - job: TerraformValidate
    steps:
    - task: TerraformTaskV2@2
      displayName: 'Terraform Validate'
      inputs:
        provider: 'azurerm'
        command: 'validate'
        workingDirectory: '$(System.DefaultWorkingDirectory)'
    
    - task: TerraformTaskV2@2
      displayName: 'Terraform Plan'
      inputs:
        provider: 'azurerm'
        command: 'plan'
        workingDirectory: '$(System.DefaultWorkingDirectory)'
```

### Change Management for CAF Landing Zones

#### **Pre-Change Repository Analysis**
**Impact Assessment Checklist:**
1. **Landing Zone Boundaries**: Verify changes don't violate CAF principles
2. **Cross-Repository Dependencies**: Identify impacts on other landing zones
3. **Security Boundary Maintenance**: Ensure security policies remain effective
4. **CIDR Management**: Prevent IP address conflicts across environments
5. **DNS Configuration**: Validate DNS resolution continues functioning

#### **Testing Protocols Through IaC**
**Non-Production Validation:**
1. **Configuration Replication**: Deploy changes to non-production environments first
2. **Connectivity Testing**: Validate all network paths through configuration analysis
3. **Security Testing**: Verify NSG and firewall rules function as expected
4. **Performance Validation**: Ensure changes don't impact network performance
5. **Rollback Procedures**: Prepare automated rollback through version control

---

## Emergency Response Procedures

### Critical Incident Response Framework

#### **Severity 1: Complete Landing Zone Connectivity Failure**
**Repository-Based Response:**
1. **Immediate Assessment**: Review recent commits in relevant repositories
2. **Configuration Comparison**: Compare current IaC with last known good configuration
3. **Emergency Rollback**: Use version control to revert to previous working configuration
4. **Parallel Deployment**: Deploy fixes to non-production for validation simultaneously
5. **Communication**: Keep stakeholders informed through documented procedures

#### **Severity 2: Partial Service Degradation**
**Systematic Repository Analysis:**
1. **Component Isolation**: Identify affected IaC components through configuration analysis
2. **Dependency Mapping**: Understand service dependencies through terraform references
3. **Configuration Validation**: Compare configurations with documented standards
4. **Targeted Fixes**: Implement specific configuration changes through IaC updates
5. **Monitoring Integration**: Ensure monitoring captures resolution effectiveness

#### **Recovery Validation Procedures**
**Post-Incident Configuration Verification:**
1. **Full Connectivity Testing**: Validate all network paths through configuration analysis
2. **Security Policy Enforcement**: Confirm NSG and firewall rules are properly applied
3. **Cross-Landing Zone Communication**: Test inter-landing zone connectivity patterns
4. **Performance Baseline**: Verify network performance returns to acceptable levels
5. **Documentation Updates**: Update IaC documentation with lessons learned

---

## Advanced Diagnostic Techniques

### UniSuper-Specific Configuration Analysis

#### **CAF Landing Zone Validation**
**Platform Landing Zone Health:**
```terraform
# Connectivity Landing Zone validation points
data "azurerm_virtual_network" "connectivity_hub" {
  name                = "vnet-connectivity-prd-mel-001"
  resource_group_name = "rg-connectivity-prd-mel-001"
}

data "azurerm_firewall" "hub_firewall" {
  name                = "afw-connectivity-prd-mel-001"
  resource_group_name = "rg-connectivity-prd-mel-001"
}

# Validate hub configuration
output "hub_validation" {
  value = {
    hub_address_space = data.azurerm_virtual_network.connectivity_hub.address_space
    firewall_private_ip = data.azurerm_firewall.hub_firewall.ip_configuration[0].private_ip_address
  }
}
```

#### **Application Landing Zone Analysis**
**Spoke Configuration Validation:**
```terraform
# Application Landing Zone connectivity validation
data "azurerm_virtual_network_peering" "spoke_to_hub" {
  name                = "peer-spoke-to-hub"
  resource_group_name = var.spoke_resource_group
  virtual_network_name = var.spoke_vnet_name
}

# Validate peering configuration
output "peering_validation" {
  value = {
    peering_state = data.azurerm_virtual_network_peering.spoke_to_hub.virtual_network_access
    gateway_transit = data.azurerm_virtual_network_peering.spoke_to_hub.allow_gateway_transit
  }
}
```

#### **Legacy Integration Analysis**
**USM Subscription Integration:**
```terraform
# Legacy subscription connectivity analysis
data "azurerm_virtual_network" "legacy_vnet" {
  name                = "vnet-inv-syd-01"
  resource_group_name = "rg-usm-production"
}

# Migration planning validation
output "legacy_integration" {
  value = {
    legacy_address_space = data.azurerm_virtual_network.legacy_vnet.address_space
    migration_compatibility = "Review CIDR conflicts with CAF landing zones"
  }
}
```

---

## Key Takeaways and Best Practices

### Critical Understanding Points for Repository-Based Troubleshooting

1. **CAF Landing Zone Architecture Recognition**
   - **Platform Landing Zones**: Connectivity, Identity, Management with shared services
   - **Application Landing Zones**: Business unit specific with standardized networking patterns
   - **Cross-Landing Zone Dependencies**: Understand service consumption patterns

2. **Hub-and-Spoke Configuration Management**
   - **Central Hub**: All traffic flows through connectivity hub with Azure Firewall
   - **Spoke Isolation**: Each application landing zone has dedicated spoke VNet
   - **Peering Configurations**: Both hub-to-spoke and spoke-to-hub peering required

3. **IaC-First Troubleshooting Approach**
   - **Configuration Analysis**: Always start with terraform/YAML configuration review
   - **Version Control**: Use git history to identify recent changes causing issues
   - **Template Validation**: Compare configurations with established patterns

4. **Security Configuration Compliance**
   - **NSG Rule Hierarchies**: Understand subnet-level and NIC-level NSG applications
   - **Firewall Centralization**: Azure Firewall provides centralized security enforcement
   - **Private Endpoint Strategy**: Use private endpoints for PaaS connectivity (except managed SQL)

5. **DNS and Service Integration**
   - **Private DNS Zones**: Centralized DNS resolution for private endpoints
   - **Service Endpoints**: Limited to managed SQL instances only
   - **Cross-Landing Zone Resolution**: DNS configuration supports all spoke VNets

### Common Repository Analysis Patterns

1. **Connectivity Issues**: Check VNet peering configurations and route table definitions
2. **Security Blocks**: Review NSG rules and Azure Firewall application/network rules
3. **DNS Resolution**: Validate private DNS zone links and private endpoint configurations
4. **Performance Issues**: Analyze firewall rule ordering and VNet peering settings
5. **Legacy Integration**: Review CIDR allocations and migration planning for USM subscriptions

This comprehensive repository-based troubleshooting approach ensures effective problem resolution while maintaining the integrity of UniSuper's Azure CAF landing zone architecture and supporting ongoing digital transformation initiatives. 