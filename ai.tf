# Storage Account
resource "azurerm_storage_account" "default" {
  name                            = module.naming.storage_account_name
  location                        = module.resource_group.location
  resource_group_name             = module.resource_group.name
  account_tier                    = "Standard"
  account_replication_type        = "LRS"
  allow_nested_items_to_be_public = false
  public_network_access_enabled   = true
  https_traffic_only_enabled      = true
  min_tls_version                 = "TLS1_2"

  tags = local.common_tags

  # for idempotency
  blob_properties {
    cors_rule {
      allowed_headers = ["*", ]
      allowed_methods = [
        "GET",
        "HEAD",
        "PUT",
        "DELETE",
        "OPTIONS",
        "POST",
        "PATCH",
      ]
      allowed_origins = [
        "https://mlworkspace.azure.ai",
        "https://ml.azure.com",
        "https://*.ml.azure.com",
        "https://ai.azure.com",
        "https://*.ai.azure.com",
      ]
      exposed_headers = [
        "*",
      ]
      max_age_in_seconds = 1800
    }
  }

  # for idempotency
  share_properties {
    cors_rule {
      allowed_headers = ["*", ]
      allowed_methods = [
        "GET",
        "HEAD",
        "PUT",
        "DELETE",
        "OPTIONS",
        "POST",
        "PATCH",
      ]
      allowed_origins = [
        "https://mlworkspace.azure.ai",
        "https://ml.azure.com",
        "https://*.ml.azure.com",
        "https://ai.azure.com",
        "https://*.ai.azure.com",
      ]
      exposed_headers = [
        "*",
      ]
      max_age_in_seconds = 1800
    }
  }
}

# Key Vault
resource "azurerm_key_vault" "default" {
  name                          = module.naming.key_vault_name
  location                      = module.resource_group.location
  resource_group_name           = module.resource_group.name
  tenant_id                     = data.azurerm_client_config.current.tenant_id
  sku_name                      = "standard"
  purge_protection_enabled      = false
  public_network_access_enabled = var.is_private
  tags                          = local.common_tags
}

# AI Services
resource "azapi_resource" "AIServicesResource" {
  type                   = "Microsoft.CognitiveServices/accounts@2024-04-01-preview"
  name                   = module.naming.azapi_resource_service_name
  location               = "australiaeast" # Change to module.resource_group.location in prod
  parent_id              = module.resource_group.id
  response_export_values = ["*"]
  tags                   = local.common_tags
  identity {
    type = "SystemAssigned"
  }
  body = {
    name = module.naming.azapi_resource_service_name
    properties = {
      customSubDomainName = "${module.naming.azapi_resource_service_name}domain"
      publicNetworkAccess = "Disabled"
      #   apiProperties = {
      #     statisticsEnabled = false
      #   }
    }
    kind = "AIServices"
    sku = {
      name = "S0" # Adjust as needed
    }
  }
  lifecycle {
    ignore_changes = [
      # When the service connection to the AI Studio Hub is created,
      # tags are added to this resource
      tags,
    ]
  }
}

# Azure AI Hub
resource "azapi_resource" "hub" {
  type      = "Microsoft.MachineLearningServices/workspaces@2024-04-01-preview"
  name      = module.naming.azapi_resource_hub_name
  location  = module.resource_group.location
  parent_id = module.resource_group.id
  tags      = local.common_tags
  identity {
    type = "SystemAssigned"
  }
  body = {
    properties = {
      description         = "This is the DevOps Agentic POV NPD Azure AI hub"
      friendlyName        = module.naming.azapi_resource_hub_name
      storageAccount      = azurerm_storage_account.default.id
      keyVault            = azurerm_key_vault.default.id
      publicNetworkAccess = "Disabled"
      # containerRegistry = local.container_registry_id # needed?
      # applicationInsights = local.application_insights_id # need to create
      # systemDatastoresAuthMode = var.storage_access_type # needed?
      managedNetwork = {
        isolationMode = var.workspace_managed_network.isolation_mode
        status = {
          sparkReady = var.workspace_managed_network.spark_ready
        }
        outboundRules = {
          for key, rule in var.outbound_rules : key => {
            type = "PrivateEndpoint"
            destination = {
              serviceResourceId = rule.resource_id
              subresourceTarget = rule.sub_resource_target
              sparkEnabled      = false
              sparkStatus       = "Inactive"
            }
          }
        }
      }
    }
    kind = "Hub"
  }
  lifecycle {
    ignore_changes = [
      # When the service connections for CognitiveServices are created,
      # tags are added to this resource
      tags,
    ]
  }
}

# Azure AI Project
resource "azapi_resource" "project" {
  type      = "Microsoft.MachineLearningServices/workspaces@2024-07-01-preview"
  name      = module.naming.azapi_resource_project_name
  location  = module.resource_group.location
  parent_id = module.resource_group.id
  tags      = local.common_tags

  identity {
    type = "SystemAssigned"
  }

  body = {
    properties = {
      description   = "DevOps Agentic POV NPD Azure AI project"
      friendlyName  = "DevOps Agentic POV NPD Azure AI project"
      hubResourceId = azapi_resource.hub.id
    }
    kind = "Project"
  }
}

# AI Services Connection
resource "azapi_resource" "AIServicesConnection" {
  type                   = "Microsoft.MachineLearningServices/workspaces/connections@2024-07-01-preview"
  name                   = module.naming.azapi_resource_connection_name
  parent_id              = azapi_resource.hub.id
  response_export_values = ["*"]

  body = {
    properties = {
      category      = "AIServices"
      target        = azapi_resource.AIServicesResource.output.properties.endpoint
      authType      = "AAD"
      isSharedToAll = true
      metadata = {
        ApiType    = "Azure"
        ResourceId = azapi_resource.AIServicesResource.id
      }
    }
  }
}

# # #Azure Machine Learning Compute Instance
# # resource "azapi_resource" "computeinstance" {
# #   type = "Microsoft.MachineLearningServices/workspaces/computes@2024-07-01-preview"
# #     tags = local.common_tags
# #   body = {
# #     properties = {
# #       computeLocation  = module.resource_group.location
# #       computeType      = "ComputeInstance"
# #       disableLocalAuth = true
# #       properties = {
# #         enableNodePublicIp = false
# #         vmSize             = "STANDARD_DS2_V2"
# #       }
# #     }
# #   }
# #   location               = module.resource_group.location
# #   name                   = "aici-${var.unique_id}-npd01"
# #   parent_id              = azapi_resource.hub.id
# #   response_export_values = ["*"]

# #   identity {
# #     type = "SystemAssigned"
# #   }
# # }

# AI Search
resource "azurerm_search_service" "ai_search" {
  name                = module.naming.search_service_name
  resource_group_name = module.resource_group.name
  location            = module.resource_group.location
  sku                 = "standard2"
  replica_count       = var.azure_ai_replica_count
  partition_count     = var.azure_ai_partition_count
  semantic_search_sku = "free"

  public_network_access_enabled = var.is_private
  allowed_ips                   = var.azure_ai_allowed_ips
  local_authentication_enabled  = true
  network_rule_bypass_option    = "AzureServices"
  identity {
    type = "SystemAssigned"
  }

  tags = local.common_tags
}

# # resource "azurerm_management_lock" "this" {
# #   lock_level = var.lock.kind
# #   name       = coalesce(var.lock.name, "lock-${var.lock.kind}")
# #   scope      = local.aml_resource.id
# #   notes      = var.lock.kind == "CanNotDelete" ? "Cannot delete the resource or its child resources." : "Cannot delete or modify the resource or its child resources."
# # }

# # app insights?
