apiVersion: v1
kind: ConfigMap
metadata:
  name: networking-agent-config
  namespace: networking
data:
  # global
  PYTHONDONTWRITEBYTECODE: "1"
  PYTHONUNBUFFERED: "1"
  TRANSPORT_TYPE: "sse"
  # A2A ENV VARs
  AZURE_OPENAI_ENDPOINT: "https://demoopenaiagent.openai.azure.com/"
  LLM_MODEL: "azure_openai:gpt-4.1" 
  MCP_URL: "http://networking-mcp-service.networking:8000"    
  OPENAI_API_VERSION: "2025-03-01-preview"
  # MCP ENV VARs
  AZURE_DEVOPS_ORG: "UniSuper-Infrastructure"
  AZURE_DEVOPS_PROJECT: "Connectivity"
  AZURE_DEVOPS_URL: "https://dev.azure.com/UniSuper-Infrastructure/"
  AZURE_OPENAI_API_VERSION: "2025-03-01-preview"
  AZURE_OPENAI_EMBEDDING_DEPLOYMENT: "agenticopenai"
  AZURE_SEARCH_INDEX_NAME: "rag-1750331434003-api"
  AZURE_SEARCH_SERVICE_ENDPOINT: "https://srch-devopsai-npd-mel.search.windows.net"
  EMBEDDING_MODEL_NAME: "text-embedding-3-small"
  # frontend (nwagt)
  A2A_URL: "http://networking-agent-service.networking:8000"