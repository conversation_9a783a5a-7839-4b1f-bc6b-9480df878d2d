.markdown-body {
  font-family: var(--font-geist-sans), -apple-system, BlinkMacSystemFont, "Segoe UI", Helvetica, Arial, sans-serif;
  font-size: 15px;
  line-height: 1.6;
  word-wrap: break-word;
  color: #374151;
}

.markdown-body a {
  color: #2563eb;
  text-decoration: none;
}

.markdown-body a:hover {
  text-decoration: underline;
}

.markdown-body strong {
  font-weight: 600;
  color: #1e40af;
}

.markdown-body h1, 
.markdown-body h2, 
.markdown-body h3, 
.markdown-body h4, 
.markdown-body h5, 
.markdown-body h6 {
  margin-top: 24px;
  margin-bottom: 16px;
  font-weight: 600;
  line-height: 1.25;
  color: #1e40af;
}

.markdown-body h1 {
  font-size: 1.75em;
  border-bottom: 1px solid #dbeafe;
  padding-bottom: 0.3em;
}

.markdown-body h2 {
  font-size: 1.5em;
  border-bottom: 1px solid #dbeafe;
  padding-bottom: 0.3em;
}

.markdown-body h3 {
  font-size: 1.25em;
}

.markdown-body h4 {
  font-size: 1em;
}

.markdown-body code {
  padding: 0.2em 0.4em;
  margin: 0;
  font-size: 85%;
  background-color: #eff6ff;
  border-radius: 3px;
  font-family: var(--font-geist-mono), monospace;
  color: #1e40af;
}

.markdown-body pre {
  word-wrap: normal;
  padding: 16px;
  overflow: auto;
  font-size: 85%;
  line-height: 1.45;
  background-color: #f8fafc;
  border-radius: 6px;
  margin-bottom: 16px;
  border: 1px solid #dbeafe;
}

.markdown-body pre code {
  display: inline;
  max-width: auto;
  padding: 0;
  margin: 0;
  overflow: visible;
  line-height: inherit;
  word-wrap: normal;
  background-color: transparent;
  border: 0;
  color: #374151;
}

.markdown-body blockquote {
  padding: 0 1em;
  color: #4b5563;
  border-left: 0.25em solid #bfdbfe;
  margin-bottom: 16px;
}

.markdown-body ul,
.markdown-body ol {
  padding-left: 2em;
  margin-bottom: 16px;
}

.markdown-body ul li,
.markdown-body ol li {
  margin-bottom: 0.25em;
}

.markdown-body table {
  display: block;
  width: 100%;
  overflow: auto;
  margin-bottom: 16px;
  border-collapse: collapse;
  border-spacing: 0;
}

.markdown-body table th {
  font-weight: 600;
  background-color: #eff6ff;
}