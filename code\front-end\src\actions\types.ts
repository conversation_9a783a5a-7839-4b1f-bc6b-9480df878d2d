export interface ThreadSummary {
	thread_id: string;
	title: string;
}

export interface ListThreadsResponse {
	threads: ThreadSummary[];
}

export interface CustomHumanMessage {
	type: "human";
	content: string;
}

export interface IntermediateStep {
	title: string;
	content: string;
}

export interface Reference {
	name: string;
	content: string;
}

export interface CustomAIMessage {
	type: "ai";
	content: string;
	intermediate_steps: IntermediateStep[];
	references: Reference[];
}

export type Message = CustomAIMessage | CustomHumanMessage;

export interface ThreadHistoryResponse {
	thread_id: string;
	messages: Message[];
}

export interface QueryResponse {
    response: CustomAIMessage;
}

export interface NewThreadIDResponse {
	thread_id: string
}