# # Creates Log Anaylytics Workspace - FOR BASELINING LOG SIZE ONLY. NEEDS TO BE REPLACED BY EVENTHUB
# resource "azurerm_log_analytics_workspace" "law" {
#   name                = module.naming.log_analytics_workspace_name
#   location            = module.resource_group.location
#   resource_group_name = module.resource_group.name
#   sku                 = "PerGB2018"
#   retention_in_days   = 30
#   tags                = module.resource_group.tags
# }

# resource "azurerm_monitor_diagnostic_setting" "aihub-diag" {
#   name                       = "aihub-diag"
#   target_resource_id         = azapi_resource.hub.id
#   log_analytics_workspace_id = azurerm_log_analytics_workspace.law.id

#   enabled_log {
#     category_group = "allLogs"
#   }
#   metric {
#     category = "AllMetrics"
#   }
# }

# resource "azurerm_monitor_diagnostic_setting" "aiproj-diag" {
#   name                       = "aiproj-diag"
#   target_resource_id         = azapi_resource.project.id
#   log_analytics_workspace_id = azurerm_log_analytics_workspace.law.id

#   enabled_log {
#     category_group = "allLogs"
#   }
#   metric {
#     category = "AllMetrics"
#   }
# }

# resource "azurerm_monitor_diagnostic_setting" "ais-diag" {
#   name                       = "ais-diag"
#   target_resource_id         = azapi_resource.AIServicesResource.id
#   log_analytics_workspace_id = azurerm_log_analytics_workspace.law.id

#   enabled_log {
#     category_group = "allLogs"
#   }
#   metric {
#     category = "AllMetrics"
#   }
# }

# resource "azurerm_monitor_diagnostic_setting" "aisearch-diag" {
#   name                       = "aisearch-diag"
#   target_resource_id         = azurerm_search_service.ai_search.id
#   log_analytics_workspace_id = azurerm_log_analytics_workspace.law.id

#   enabled_log {
#     category_group = "allLogs"
#   }
#   metric {
#     category = "AllMetrics"
#   }
# }
