import logging
import click
from networking_agent.a2a.common.types import AgentSkill, AgentCapabilities, AgentCard
from networking_agent.a2a.task_manager import Agent<PERSON><PERSON><PERSON>anager
from networking_agent.a2a.custom_server import CustomA2AServer


logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@click.command()
@click.option("--host", default="0.0.0.0")
@click.option("--port", default=8000)
def main(host, port):
  skill = AgentSkill(
	id="my-project-echo-skill",
	name="Echo Tool",
	description="Echos the input given",
	tags=["echo", "repeater"],
	examples=["I will see this echoed back to me"],
	inputModes=["text"],
	outputModes=["text"],
  )
  logging.info(skill)
  capabilities = AgentCapabilities()
  agent_card = AgentCard(
	name="Echo Agent",
	description="This agent echos the input given",
	url=f"http://{host}:{port}/",
	version="0.1.0",
	defaultInputModes=["text"],
	defaultOutputModes=["text"],
	capabilities=capabilities,
	skills=[skill]
  )
  logging.info(agent_card)
  task_manager = AgentTaskManager()
  server = CustomA2AServer(
	agent_card=agent_card,
	task_manager=task_manager,
	host=host,
	port=port,
  )
  server.start()

class SimpleRequest:
	def __init__(self, data):
		self.data = data

	async def json(self):
		return self.data

if __name__ == "__main__":
	main()