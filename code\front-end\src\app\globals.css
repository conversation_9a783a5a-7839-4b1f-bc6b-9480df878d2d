@import "tailwindcss";


:root {
  --foreground-rgb: 15, 23, 42;
  --background-rgb: 255, 255, 255;
}

body {
  color: rgb(var(--foreground-rgb));
  background: rgb(var(--background-rgb));
  overflow: hidden;
}

/* Custom scrollbar for webkit browsers */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #dce4e8;
}

::-webkit-scrollbar-thumb {
  background: #96989a;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #525353;
}

/* Add or update these styles */

/* Improve base typography */
body {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

/* Adjust heading styles */
h1, h2, h3, h4, h5, h6 {
  letter-spacing: -0.025em;
}

/* Improve readability of body text */
p, li, input, button {
  line-height: 1.5;
}

/* Better font weights */
.font-light {
  font-weight: 300;
}

.font-normal {
  font-weight: 400;
}

.font-medium {
  font-weight: 500;
}

.font-semibold {
  font-weight: 600;
}

.font-bold {
  font-weight: 700;
}

/* Improved letter spacing for better readability */
.tracking-tighter {
  letter-spacing: -0.05em;
}

.tracking-tight {
  letter-spacing: -0.025em;
}

.tracking-normal {
  letter-spacing: 0;
}

.tracking-wide {
  letter-spacing: 0.025em;
}

.tracking-wider {
  letter-spacing: 0.05em;
}

/* Font size utilities with appropriate line heights */
.text-xs {
  font-size: 0.75rem;
  line-height: 1.25;
}

.text-sm {
  font-size: 0.875rem;
  line-height: 1.4;
}

.text-base {
  font-size: 1rem;
  line-height: 1.5;
}

.text-lg {
  font-size: 1.125rem;
  line-height: 1.55;
}

.text-xl {
  font-size: 1.25rem;
  line-height: 1.6;
}

.text-2xl {
  font-size: 1.5rem;
  line-height: 1.3;
}
