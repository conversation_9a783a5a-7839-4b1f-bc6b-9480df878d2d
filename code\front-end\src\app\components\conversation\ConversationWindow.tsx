"use client";

import React, { useRef, useEffect } from "react";
import ChatMessage from "../ChatMessage";
import AttachmentList from "./AttachmentList"; // Import AttachmentList component
import { logger } from "../../utils/logger";
import { UIMessage, ConversationWindowProps } from "../../types";

export default function ConversationWindow({
  messages,
  isLoading,
  currentReasoning,
  showCreateTicket = false,
}: ConversationWindowProps) {
  const messagesEndRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [messages, isLoading, currentReasoning]);

  const handleCloseReasoning = () => {
    logger.log("Reasoning closed");
  };

  return (
    <div className="flex-1 overflow-y-auto p-6 space-y-6 bg-white">
      {showCreateTicket ? (
        <ChatMessage
          id="ticket-form"
          type="ai"
          content=""
          timestamp={new Date()}
          showCreateTicketMessage={true}
        />
      ) : messages.length === 0 && !isLoading ? (
        <div className="flex items-center justify-center h-full text-gray-500">
          <div className="text-center max-w-md p-8 bg-white">
            <div className="w-16 h-16 rounded-full bg-blue-600 flex items-center justify-center mx-auto mb-4 relative">
              <img
                src="/merlin_dog.png"
                alt="Merlin Logo"
                width="50"
                height="50"
                className="object-contain absolute transform -translate-y-0.5"
              />
            </div>
            <h3 className="text-xl font-medium mb-2 text-gray-800">
              Hi, there
            </h3>
            <div className="text-sm text-gray-500">
              Tell me what you need, and I'll handle the rest.
            </div>
          </div>
        </div>
      ) : (
        <>
          {messages.map((message, idx) => (
            <ChatMessage
              key={message.id ? `${message.id}-${idx}` : idx}
              id={message.id}
              type={message.type}
              content={message.content}
              timestamp={message.timestamp}
              intermediate_steps={message.type === "ai" ? message.intermediate_steps : undefined}
              references={message.type === "ai" ? message.references : undefined}
              onCloseReasoning={handleCloseReasoning}
              showAttachments={
                message.type === "ai" &&
                idx === messages.length - 1 &&
                !isLoading
              }
            />
          ))}
          {/* Show thinking message when loading */}
          {isLoading && currentReasoning && (
            <ChatMessage
              id="thinking"
              type="ai"
              content=""
              timestamp={new Date()}
              intermediate_steps={currentReasoning.steps}
              onCloseReasoning={handleCloseReasoning}
              isLoading={isLoading}
            />
          )}
        </>
      )}
      <div ref={messagesEndRef} />
    </div>
  );
}
